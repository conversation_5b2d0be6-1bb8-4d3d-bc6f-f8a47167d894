Date : 2025-08-21 16:21:05
Directory : d:\mydev\Qiankun-Pouch\src
Total : 232 files,  56117 codes, 9955 comments, 8302 blanks, all 74374 lines

Languages
+----------------+------------+------------+------------+------------+------------+
| language       | files      | code       | comment    | blank      | total      |
+----------------+------------+------------+------------+------------+------------+
| TypeScript JSX |        138 |     29,110 |      2,735 |      3,122 |     34,967 |
| TypeScript     |         85 |     26,058 |      7,154 |      4,981 |     38,193 |
| PostCSS        |          3 |        846 |         66 |        195 |      1,107 |
| HTML           |          6 |        103 |          0 |          4 |        107 |
+----------------+------------+------------+------------+------------+------------+

Directories
+--------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                           | files      | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                              |        232 |     56,117 |      9,955 |      8,302 |     74,374 |
| background                                                                     |          2 |        874 |        207 |        158 |      1,239 |
| components                                                                     |        101 |     22,587 |      2,130 |      2,455 |     27,172 |
| components (Files)                                                             |         47 |     14,603 |      1,700 |      1,638 |     17,941 |
| components\ConflictResolution                                                  |          4 |        216 |          8 |         23 |        247 |
| components\HelpTooltip                                                         |          2 |        166 |         26 |         22 |        214 |
| components\examples                                                            |          9 |      1,492 |         99 |        133 |      1,724 |
| components\test                                                                |         21 |      4,966 |        297 |        486 |      5,749 |
| components\ui                                                                  |         18 |      1,144 |          0 |        153 |      1,297 |
| constants                                                                      |          2 |        251 |         21 |         26 |        298 |
| content                                                                        |          2 |        150 |         19 |         31 |        200 |
| demo                                                                           |          5 |         81 |          3 |          9 |         93 |
| examples                                                                       |         14 |      2,578 |        187 |        245 |      3,010 |
| hooks                                                                          |          7 |        927 |        154 |        179 |      1,260 |
| lib                                                                            |          2 |        232 |         39 |         36 |        307 |
| options                                                                        |         28 |      3,579 |        460 |        469 |      4,508 |
| options (Files)                                                                |          3 |         88 |         14 |         15 |        117 |
| options\components                                                             |         13 |      1,869 |        140 |        186 |      2,195 |
| options\constants                                                              |          1 |         66 |         10 |          7 |         83 |
| options\data                                                                   |          2 |        327 |         15 |         12 |        354 |
| options\hooks                                                                  |          6 |        749 |        138 |        154 |      1,041 |
| options\utils                                                                  |          3 |        480 |        143 |         95 |        718 |
| pages                                                                          |          1 |          6 |          5 |          3 |         14 |
| popup                                                                          |          8 |      1,738 |        194 |        173 |      2,105 |
| popup (Files)                                                                  |          3 |        641 |         55 |         64 |        760 |
| popup\components                                                               |          5 |      1,097 |        139 |        109 |      1,345 |
| services                                                                       |         27 |     14,987 |      4,227 |      2,811 |     22,025 |
| styles                                                                         |          2 |        774 |         59 |        184 |      1,017 |
| test-pages                                                                     |          2 |        216 |          4 |         16 |        236 |
| types                                                                          |          7 |      1,303 |        258 |        236 |      1,797 |
| utils                                                                          |         21 |      5,424 |      1,836 |      1,167 |      8,427 |
| workers                                                                        |          1 |        410 |        152 |        104 |        666 |
+--------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+--------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| filename                                                                       | language       | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| d:\mydev\Qiankun-Pouch\src\background\index.ts                                 | TypeScript     |         92 |         18 |         22 |        132 |
| d:\mydev\Qiankun-Pouch\src\background\messageHandler.ts                        | TypeScript     |        782 |        189 |        136 |      1,107 |
| d:\mydev\Qiankun-Pouch\src\components\AIConfigPanel.tsx                        | TypeScript JSX |        411 |         19 |         45 |        475 |
| d:\mydev\Qiankun-Pouch\src\components\AIIntegrationTab.tsx                     | TypeScript JSX |        834 |         57 |         88 |        979 |
| d:\mydev\Qiankun-Pouch\src\components\AIRecommendations.tsx                    | TypeScript JSX |        553 |        131 |         92 |        776 |
| d:\mydev\Qiankun-Pouch\src\components\AITextGenerator.tsx                      | TypeScript JSX |        270 |         39 |         30 |        339 |
| d:\mydev\Qiankun-Pouch\src\components\AddBookmarkModal.tsx                     | TypeScript JSX |        551 |         45 |         48 |        644 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkCompact.tsx                      | TypeScript JSX |        265 |         39 |         25 |        329 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkEditModal.tsx                    | TypeScript JSX |        603 |         53 |         58 |        714 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkRow.tsx                          | TypeScript JSX |        229 |         35 |         22 |        286 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkSortSelector.tsx                 | TypeScript JSX |         45 |         18 |          9 |         72 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkToolbar.tsx                      | TypeScript JSX |        227 |         31 |         31 |        289 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarksTab.tsx                         | TypeScript JSX |        424 |         92 |         58 |        574 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryCard.tsx                         | TypeScript JSX |        219 |         40 |         28 |        287 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryForm.tsx                         | TypeScript JSX |        294 |         33 |         33 |        360 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryList.tsx                         | TypeScript JSX |        114 |         20 |         14 |        148 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryManagementTab.tsx                | TypeScript JSX |        208 |         31 |         36 |        275 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryModal.tsx                        | TypeScript JSX |        205 |         32 |         24 |        261 |
| d:\mydev\Qiankun-Pouch\src\components\ConflictResolutionDialog.tsx             | TypeScript JSX |        283 |         19 |         26 |        328 |
| d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\BatchActionsPanel.tsx | TypeScript JSX |         34 |          1 |          4 |         39 |
| d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\ConflictListPanel.tsx | TypeScript JSX |         74 |          1 |          8 |         83 |
| d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\ManualEditForm.tsx    | TypeScript JSX |         72 |          1 |          7 |         80 |
| d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\utils.ts              | TypeScript     |         36 |          5 |          4 |         45 |
| d:\mydev\Qiankun-Pouch\src\components\DefaultAIModelsTab.tsx                   | TypeScript JSX |        520 |         31 |         50 |        601 |
| d:\mydev\Qiankun-Pouch\src\components\DeleteConfirmModal.tsx                   | TypeScript JSX |        221 |         25 |         24 |        270 |
| d:\mydev\Qiankun-Pouch\src\components\HelpTooltip.tsx                          | TypeScript JSX |        243 |         27 |         29 |        299 |
| d:\mydev\Qiankun-Pouch\src\components\HelpTooltip\helpContent.ts               | TypeScript     |         73 |         10 |          4 |         87 |
| d:\mydev\Qiankun-Pouch\src\components\HelpTooltip\useTooltip.ts                | TypeScript     |         93 |         16 |         18 |        127 |
| d:\mydev\Qiankun-Pouch\src\components\ImportExportTab.tsx                      | TypeScript JSX |        719 |         43 |         78 |        840 |
| d:\mydev\Qiankun-Pouch\src\components\LazyLoadWrapper.tsx                      | TypeScript JSX |        302 |         31 |         53 |        386 |
| d:\mydev\Qiankun-Pouch\src\components\LoadingIndicator.tsx                     | TypeScript JSX |         86 |         19 |         13 |        118 |
| d:\mydev\Qiankun-Pouch\src\components\MCPSettingsTab.tsx                       | TypeScript JSX |        884 |         48 |         82 |      1,014 |
| d:\mydev\Qiankun-Pouch\src\components\ManagementPageLayout.tsx                 | TypeScript JSX |         81 |         25 |         11 |        117 |
| d:\mydev\Qiankun-Pouch\src\components\ModelSelector.tsx                        | TypeScript JSX |        335 |         10 |         21 |        366 |
| d:\mydev\Qiankun-Pouch\src\components\NotionSyncTab.tsx                        | TypeScript JSX |        366 |         18 |         23 |        407 |
| d:\mydev\Qiankun-Pouch\src\components\ObsidianIntegrationTab.tsx               | TypeScript JSX |        667 |         23 |         52 |        742 |
| d:\mydev\Qiankun-Pouch\src\components\OptionsPageErrorBoundary.tsx             | TypeScript JSX |        238 |         40 |         36 |        314 |
| d:\mydev\Qiankun-Pouch\src\components\SmartFolderSelector.tsx                  | TypeScript JSX |        274 |         54 |         34 |        362 |
| d:\mydev\Qiankun-Pouch\src\components\SmartTagInput.tsx                        | TypeScript JSX |        296 |         56 |         31 |        383 |
| d:\mydev\Qiankun-Pouch\src\components\SuperMarketTab.tsx                       | TypeScript JSX |        445 |         23 |         29 |        497 |
| d:\mydev\Qiankun-Pouch\src\components\TagBatchActions.tsx                      | TypeScript JSX |        328 |         26 |         24 |        378 |
| d:\mydev\Qiankun-Pouch\src\components\TagCard.tsx                              | TypeScript JSX |        209 |         39 |         30 |        278 |
| d:\mydev\Qiankun-Pouch\src\components\TagCloud.tsx                             | TypeScript JSX |        149 |         28 |         20 |        197 |
| d:\mydev\Qiankun-Pouch\src\components\TagColorPicker.tsx                       | TypeScript JSX |        232 |         48 |         23 |        303 |
| d:\mydev\Qiankun-Pouch\src\components\TagForm.tsx                              | TypeScript JSX |        285 |         66 |         40 |        391 |
| d:\mydev\Qiankun-Pouch\src\components\TagList.tsx                              | TypeScript JSX |        415 |         55 |         46 |        516 |
| d:\mydev\Qiankun-Pouch\src\components\TagManagementTab.tsx                     | TypeScript JSX |        323 |         44 |         58 |        425 |
| d:\mydev\Qiankun-Pouch\src\components\TagModal.tsx                             | TypeScript JSX |        212 |         34 |         25 |        271 |
| d:\mydev\Qiankun-Pouch\src\components\TagsTab.tsx                              | TypeScript JSX |        133 |         20 |         18 |        171 |
| d:\mydev\Qiankun-Pouch\src\components\Toast.tsx                                | TypeScript JSX |        119 |         21 |         17 |        157 |
| d:\mydev\Qiankun-Pouch\src\components\ToastContainer.tsx                       | TypeScript JSX |         38 |          8 |          5 |         51 |
| d:\mydev\Qiankun-Pouch\src\components\TruncatedTitle.tsx                       | TypeScript JSX |        138 |         31 |         27 |        196 |
| d:\mydev\Qiankun-Pouch\src\components\ViewModeSelector.tsx                     | TypeScript JSX |        104 |         15 |         10 |        129 |
| d:\mydev\Qiankun-Pouch\src\components\VirtualBookmarkList.tsx                  | TypeScript JSX |        276 |         22 |         24 |        322 |
| d:\mydev\Qiankun-Pouch\src\components\VirtualScrollList.tsx                    | TypeScript JSX |        230 |         36 |         38 |        304 |
| d:\mydev\Qiankun-Pouch\src\components\examples\AddBookmarkModalDemo.tsx        | TypeScript JSX |        165 |         17 |         18 |        200 |
| d:\mydev\Qiankun-Pouch\src\components\examples\AdvancedComponentsDemo.tsx      | TypeScript JSX |        167 |          9 |         11 |        187 |
| d:\mydev\Qiankun-Pouch\src\components\examples\BasicComponentsDemo.tsx         | TypeScript JSX |        176 |          8 |          8 |        192 |
| d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkCompactDemo.tsx         | TypeScript JSX |        291 |         10 |         21 |        322 |
| d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkEditModalDemo.tsx       | TypeScript JSX |        108 |         10 |         13 |        131 |
| d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkRowDemo.tsx             | TypeScript JSX |        149 |          6 |         11 |        166 |
| d:\mydev\Qiankun-Pouch\src\components\examples\DeleteConfirmModalDemo.tsx      | TypeScript JSX |        178 |         14 |         21 |        213 |
| d:\mydev\Qiankun-Pouch\src\components\examples\PopupAppDemo.tsx                | TypeScript JSX |         56 |          9 |          8 |         73 |
| d:\mydev\Qiankun-Pouch\src\components\examples\VirtualBookmarkListDemo.tsx     | TypeScript JSX |        202 |         16 |         22 |        240 |
| d:\mydev\Qiankun-Pouch\src\components\test\AIGeneratorTest.tsx                 | TypeScript JSX |        361 |         28 |         30 |        419 |
| d:\mydev\Qiankun-Pouch\src\components\test\AIModelChatTest.tsx                 | TypeScript JSX |        659 |         35 |         75 |        769 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarkCompactTest.tsx             | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarkEditModalTest.tsx           | TypeScript JSX |        223 |         12 |         18 |        253 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarkSortTest.tsx                | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarkTagColorTest.tsx            | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarkToolbarTest.tsx             | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\components\test\BookmarksTabTestPage.tsx            | TypeScript JSX |        233 |         19 |         18 |        270 |
| d:\mydev\Qiankun-Pouch\src\components\test\CardImportTest.tsx                  | TypeScript JSX |         18 |          1 |          3 |         22 |
| d:\mydev\Qiankun-Pouch\src\components\test\CloudAIServiceTest.tsx              | TypeScript JSX |        814 |         35 |         85 |        934 |
| d:\mydev\Qiankun-Pouch\src\components\test\DeleteConfirmModalTest.tsx          | TypeScript JSX |        321 |         20 |         26 |        367 |
| d:\mydev\Qiankun-Pouch\src\components\test\DeleteConfirmModalTestPage.tsx      | TypeScript JSX |        418 |         23 |         30 |        471 |
| d:\mydev\Qiankun-Pouch\src\components\test\LocalAIServiceTestPage.tsx          | TypeScript JSX |        540 |         27 |         67 |        634 |
| d:\mydev\Qiankun-Pouch\src\components\test\OtherComponentsTestPage.tsx         | TypeScript JSX |        274 |          9 |         21 |        304 |
| d:\mydev\Qiankun-Pouch\src\components\test\PopupAppTest.tsx                    | TypeScript JSX |        150 |         18 |         18 |        186 |
| d:\mydev\Qiankun-Pouch\src\components\test\PopupAppTestPage.tsx                | TypeScript JSX |          6 |          5 |          2 |         13 |
| d:\mydev\Qiankun-Pouch\src\components\test\ShadcnModalTest.tsx                 | TypeScript JSX |        173 |          9 |         15 |        197 |
| d:\mydev\Qiankun-Pouch\src\components\test\ShadcnTest.tsx                      | TypeScript JSX |         26 |          7 |          4 |         37 |
| d:\mydev\Qiankun-Pouch\src\components\test\TestErrorBoundary.tsx               | TypeScript JSX |        106 |          7 |         15 |        128 |
| d:\mydev\Qiankun-Pouch\src\components\test\VirtualBookmarkListTest.tsx         | TypeScript JSX |        352 |         26 |         35 |        413 |
| d:\mydev\Qiankun-Pouch\src\components\test\VirtualBookmarkListTestPage.tsx     | TypeScript JSX |        252 |         12 |         12 |        276 |
| d:\mydev\Qiankun-Pouch\src\components\ui\alert-dialog.tsx                      | TypeScript JSX |        126 |          0 |         14 |        140 |
| d:\mydev\Qiankun-Pouch\src\components\ui\alert.tsx                             | TypeScript JSX |         53 |          0 |          7 |         60 |
| d:\mydev\Qiankun-Pouch\src\components\ui\badge.tsx                             | TypeScript JSX |         31 |          0 |          6 |         37 |
| d:\mydev\Qiankun-Pouch\src\components\ui\button.tsx                            | TypeScript JSX |         51 |          0 |          6 |         57 |
| d:\mydev\Qiankun-Pouch\src\components\ui\card.tsx                              | TypeScript JSX |         71 |          0 |          9 |         80 |
| d:\mydev\Qiankun-Pouch\src\components\ui\checkbox.tsx                          | TypeScript JSX |         25 |          0 |          4 |         29 |
| d:\mydev\Qiankun-Pouch\src\components\ui\dialog.tsx                            | TypeScript JSX |        108 |          0 |         13 |        121 |
| d:\mydev\Qiankun-Pouch\src\components\ui\dropdown-menu.tsx                     | TypeScript JSX |        181 |          0 |         18 |        199 |
| d:\mydev\Qiankun-Pouch\src\components\ui\form.tsx                              | TypeScript JSX |        152 |          0 |         25 |        177 |
| d:\mydev\Qiankun-Pouch\src\components\ui\input.tsx                             | TypeScript JSX |         19 |          0 |          4 |         23 |
| d:\mydev\Qiankun-Pouch\src\components\ui\label.tsx                             | TypeScript JSX |         20 |          0 |          5 |         25 |
| d:\mydev\Qiankun-Pouch\src\components\ui\progress.tsx                          | TypeScript JSX |         23 |          0 |          4 |         27 |
| d:\mydev\Qiankun-Pouch\src\components\ui\select.tsx                            | TypeScript JSX |        145 |          0 |         13 |        158 |
| d:\mydev\Qiankun-Pouch\src\components\ui\separator.tsx                         | TypeScript JSX |         26 |          0 |          4 |         30 |
| d:\mydev\Qiankun-Pouch\src\components\ui\switch.tsx                            | TypeScript JSX |         24 |          0 |          4 |         28 |
| d:\mydev\Qiankun-Pouch\src\components\ui\tabs.tsx                              | TypeScript JSX |         47 |          0 |          6 |         53 |
| d:\mydev\Qiankun-Pouch\src\components\ui\textarea.tsx                          | TypeScript JSX |         20 |          0 |          4 |         24 |
| d:\mydev\Qiankun-Pouch\src\components\ui\tooltip.tsx                           | TypeScript JSX |         22 |          0 |          7 |         29 |
| d:\mydev\Qiankun-Pouch\src\constants\index.ts                                  | TypeScript     |          1 |          2 |          1 |          4 |
| d:\mydev\Qiankun-Pouch\src\constants\shadcn.ts                                 | TypeScript     |        250 |         19 |         25 |        294 |
| d:\mydev\Qiankun-Pouch\src\content\index.ts                                    | TypeScript     |         78 |         12 |         20 |        110 |
| d:\mydev\Qiankun-Pouch\src\content\style.css                                   | PostCSS        |         72 |          7 |         11 |         90 |
| d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModal.html                          | HTML           |         20 |          0 |          1 |         21 |
| d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModalTest.html                      | HTML           |         26 |          0 |          1 |         27 |
| d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModalTest.tsx                       | TypeScript JSX |         12 |          2 |          3 |         17 |
| d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.html                    | HTML           |         13 |          0 |          1 |         14 |
| d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.tsx                     | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\examples\BookmarkSortDemo.tsx                       | TypeScript JSX |        279 |         11 |         16 |        306 |
| d:\mydev\Qiankun-Pouch\src\examples\BookmarkTagColorDemo.tsx                   | TypeScript JSX |        233 |         12 |         13 |        258 |
| d:\mydev\Qiankun-Pouch\src\examples\BookmarkToolbarDemo.tsx                    | TypeScript JSX |        242 |         17 |         36 |        295 |
| d:\mydev\Qiankun-Pouch\src\examples\CategoryCardDemo.tsx                       | TypeScript JSX |        147 |          2 |         12 |        161 |
| d:\mydev\Qiankun-Pouch\src\examples\CategoryFormDemo.tsx                       | TypeScript JSX |        208 |         13 |         27 |        248 |
| d:\mydev\Qiankun-Pouch\src\examples\CategoryModalDemo.tsx                      | TypeScript JSX |        203 |         20 |         22 |        245 |
| d:\mydev\Qiankun-Pouch\src\examples\ManagementLayoutDemo.tsx                   | TypeScript JSX |        168 |          4 |         15 |        187 |
| d:\mydev\Qiankun-Pouch\src\examples\TagCardDemo.tsx                            | TypeScript JSX |        267 |         22 |         17 |        306 |
| d:\mydev\Qiankun-Pouch\src\examples\TagColorPickerDemo.tsx                     | TypeScript JSX |        163 |         12 |         11 |        186 |
| d:\mydev\Qiankun-Pouch\src\examples\TagFormDemo.tsx                            | TypeScript JSX |        238 |         25 |         21 |        284 |
| d:\mydev\Qiankun-Pouch\src\examples\TagListDemo.tsx                            | TypeScript JSX |        184 |         18 |         23 |        225 |
| d:\mydev\Qiankun-Pouch\src\examples\TagManagementTabDemo.tsx                   | TypeScript JSX |         14 |          5 |          3 |         22 |
| d:\mydev\Qiankun-Pouch\src\examples\TagModalDemo.tsx                           | TypeScript JSX |        133 |         18 |         18 |        169 |
| d:\mydev\Qiankun-Pouch\src\examples\ViewModeSelectorExample.tsx                | TypeScript JSX |         99 |          8 |         11 |        118 |
| d:\mydev\Qiankun-Pouch\src\hooks\useAdvancedSearch.ts                          | TypeScript     |        249 |         44 |         48 |        341 |
| d:\mydev\Qiankun-Pouch\src\hooks\useConflictResolution.ts                      | TypeScript     |        143 |         18 |         23 |        184 |
| d:\mydev\Qiankun-Pouch\src\hooks\useLoadingState.ts                            | TypeScript     |         63 |         17 |         11 |         91 |
| d:\mydev\Qiankun-Pouch\src\hooks\useShadcn.ts                                  | TypeScript     |        308 |         16 |         61 |        385 |
| d:\mydev\Qiankun-Pouch\src\hooks\useTagColors.ts                               | TypeScript     |         48 |         26 |         12 |         86 |
| d:\mydev\Qiankun-Pouch\src\hooks\useToast.ts                                   | TypeScript     |         72 |         21 |         15 |        108 |
| d:\mydev\Qiankun-Pouch\src\hooks\useViewMode.ts                                | TypeScript     |         44 |         12 |          9 |         65 |
| d:\mydev\Qiankun-Pouch\src\lib\shadcn-config.ts                                | TypeScript     |        227 |         39 |         34 |        300 |
| d:\mydev\Qiankun-Pouch\src\lib\utils.ts                                        | TypeScript     |          5 |          0 |          2 |          7 |
| d:\mydev\Qiankun-Pouch\src\options\OptionsApp.tsx                              | TypeScript JSX |         61 |         13 |         13 |         87 |
| d:\mydev\Qiankun-Pouch\src\options\components\AIAssistantTab.tsx               | TypeScript JSX |         66 |          2 |          6 |         74 |
| d:\mydev\Qiankun-Pouch\src\options\components\AboutTab.tsx                     | TypeScript JSX |        260 |         19 |         25 |        304 |
| d:\mydev\Qiankun-Pouch\src\options\components\AppHeader.tsx                    | TypeScript JSX |         33 |          3 |          4 |         40 |
| d:\mydev\Qiankun-Pouch\src\options\components\ErrorState.tsx                   | TypeScript JSX |         62 |          1 |          3 |         66 |
| d:\mydev\Qiankun-Pouch\src\options\components\HelpCenterTab.tsx                | TypeScript JSX |        469 |         33 |         38 |        540 |
| d:\mydev\Qiankun-Pouch\src\options\components\HelpSearchBox.tsx                | TypeScript JSX |        217 |         26 |         32 |        275 |
| d:\mydev\Qiankun-Pouch\src\options\components\LoadingState.tsx                 | TypeScript JSX |         22 |          1 |          3 |         26 |
| d:\mydev\Qiankun-Pouch\src\options\components\NavigationSidebar.tsx            | TypeScript JSX |        118 |          2 |          5 |        125 |
| d:\mydev\Qiankun-Pouch\src\options\components\PageErrorBoundary.tsx            | TypeScript JSX |        231 |         36 |         39 |        306 |
| d:\mydev\Qiankun-Pouch\src\options\components\SettingsTab.tsx                  | TypeScript JSX |         84 |          2 |          6 |         92 |
| d:\mydev\Qiankun-Pouch\src\options\components\SyncTab.tsx                      | TypeScript JSX |         77 |          2 |          6 |         85 |
| d:\mydev\Qiankun-Pouch\src\options\components\TabContentRenderer.tsx           | TypeScript JSX |        101 |          5 |          8 |        114 |
| d:\mydev\Qiankun-Pouch\src\options\components\ThemeToggle.tsx                  | TypeScript JSX |        129 |          8 |         11 |        148 |
| d:\mydev\Qiankun-Pouch\src\options\constants\tabsConfig.ts                     | TypeScript     |         66 |         10 |          7 |         83 |
| d:\mydev\Qiankun-Pouch\src\options\data\aboutInfo.ts                           | TypeScript     |         52 |          5 |          3 |         60 |
| d:\mydev\Qiankun-Pouch\src\options\data\helpContent.ts                         | TypeScript     |        275 |         10 |          9 |        294 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useAppInitialization.ts               | TypeScript     |         40 |          7 |          8 |         55 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useCache.ts                           | TypeScript     |        277 |         35 |         64 |        376 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useLazyLoad.ts                        | TypeScript     |        158 |         18 |         26 |        202 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useResponsive.ts                      | TypeScript     |        119 |         34 |         23 |        176 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useTabNavigation.ts                   | TypeScript     |         77 |         11 |         14 |        102 |
| d:\mydev\Qiankun-Pouch\src\options\hooks\useTheme.ts                           | TypeScript     |         78 |         33 |         19 |        130 |
| d:\mydev\Qiankun-Pouch\src\options\index.html                                  | HTML           |         12 |          0 |          0 |         12 |
| d:\mydev\Qiankun-Pouch\src\options\index.tsx                                   | TypeScript JSX |         15 |          1 |          2 |         18 |
| d:\mydev\Qiankun-Pouch\src\options\utils\helpSearch.ts                         | TypeScript     |        133 |         39 |         30 |        202 |
| d:\mydev\Qiankun-Pouch\src\options\utils\manifestReader.ts                     | TypeScript     |        127 |         32 |         24 |        183 |
| d:\mydev\Qiankun-Pouch\src\options\utils\performance.ts                        | TypeScript     |        220 |         72 |         41 |        333 |
| d:\mydev\Qiankun-Pouch\src\pages\test-deleteconfirmmodal.tsx                   | TypeScript JSX |          6 |          5 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\popup\PopupApp.tsx                                  | TypeScript JSX |        617 |         54 |         62 |        733 |
| d:\mydev\Qiankun-Pouch\src\popup\components\DetailedBookmarkForm.tsx           | TypeScript JSX |        634 |         51 |         58 |        743 |
| d:\mydev\Qiankun-Pouch\src\popup\components\QuickBookmarkForm.tsx              | TypeScript JSX |        209 |         47 |         22 |        278 |
| d:\mydev\Qiankun-Pouch\src\popup\components\SmartRecognition.tsx               | TypeScript JSX |        197 |         41 |         24 |        262 |
| d:\mydev\Qiankun-Pouch\src\popup\components\Toggle.tsx                         | TypeScript JSX |         55 |          0 |          5 |         60 |
| d:\mydev\Qiankun-Pouch\src\popup\components\index.ts                           | TypeScript     |          2 |          0 |          0 |          2 |
| d:\mydev\Qiankun-Pouch\src\popup\index.html                                    | HTML           |         12 |          0 |          0 |         12 |
| d:\mydev\Qiankun-Pouch\src\popup\index.tsx                                     | TypeScript JSX |         12 |          1 |          2 |         15 |
| d:\mydev\Qiankun-Pouch\src\services\BookmarkImportExportService.ts             | TypeScript     |      1,028 |        172 |        181 |      1,381 |
| d:\mydev\Qiankun-Pouch\src\services\CacheManager.ts                            | TypeScript     |        467 |        223 |         98 |        788 |
| d:\mydev\Qiankun-Pouch\src\services\ConflictResolverService.ts                 | TypeScript     |        333 |        123 |         81 |        537 |
| d:\mydev\Qiankun-Pouch\src\services\ErrorFeedbackService.ts                    | TypeScript     |        478 |         95 |         73 |        646 |
| d:\mydev\Qiankun-Pouch\src\services\ErrorRecoveryService.ts                    | TypeScript     |        341 |        102 |         60 |        503 |
| d:\mydev\Qiankun-Pouch\src\services\MemoryOptimizedProcessor.ts                | TypeScript     |        436 |        126 |         95 |        657 |
| d:\mydev\Qiankun-Pouch\src\services\SecurityValidator.ts                       | TypeScript     |        476 |        117 |         87 |        680 |
| d:\mydev\Qiankun-Pouch\src\services\ViewPreferenceService.ts                   | TypeScript     |        175 |         46 |         21 |        242 |
| d:\mydev\Qiankun-Pouch\src\services\WorkerManager.ts                           | TypeScript     |        286 |        121 |         57 |        464 |
| d:\mydev\Qiankun-Pouch\src\services\aiCacheService.ts                          | TypeScript     |        353 |        142 |         76 |        571 |
| d:\mydev\Qiankun-Pouch\src\services\aiChatService.ts                           | TypeScript     |        306 |         60 |         60 |        426 |
| d:\mydev\Qiankun-Pouch\src\services\aiConfigService.ts                         | TypeScript     |        493 |        111 |         82 |        686 |
| d:\mydev\Qiankun-Pouch\src\services\aiIntegrationService.ts                    | TypeScript     |        628 |        135 |         89 |        852 |
| d:\mydev\Qiankun-Pouch\src\services\aiModelService.ts                          | TypeScript     |        537 |        193 |        119 |        849 |
| d:\mydev\Qiankun-Pouch\src\services\aiProviderService.ts                       | TypeScript     |      3,794 |        870 |        623 |      5,287 |
| d:\mydev\Qiankun-Pouch\src\services\aiRecommendationService.ts                 | TypeScript     |        470 |        122 |         99 |        691 |
| d:\mydev\Qiankun-Pouch\src\services\aiService.ts                               | TypeScript     |      1,357 |        339 |        285 |      1,981 |
| d:\mydev\Qiankun-Pouch\src\services\bookmarkService.ts                         | TypeScript     |        393 |        167 |         84 |        644 |
| d:\mydev\Qiankun-Pouch\src\services\bookmarkStatusService.ts                   | TypeScript     |        134 |         69 |         30 |        233 |
| d:\mydev\Qiankun-Pouch\src\services\categoryService.ts                         | TypeScript     |        439 |        183 |         92 |        714 |
| d:\mydev\Qiankun-Pouch\src\services\defaultAIModelAPI.ts                       | TypeScript     |        195 |         83 |         42 |        320 |
| d:\mydev\Qiankun-Pouch\src\services\defaultAIModelService.ts                   | TypeScript     |        364 |        103 |         61 |        528 |
| d:\mydev\Qiankun-Pouch\src\services\localAIServiceAdapter.ts                   | TypeScript     |        387 |        120 |         74 |        581 |
| d:\mydev\Qiankun-Pouch\src\services\mcpTestService.ts                          | TypeScript     |        208 |         59 |         41 |        308 |
| d:\mydev\Qiankun-Pouch\src\services\pageInfoService.ts                         | TypeScript     |        277 |        100 |         62 |        439 |
| d:\mydev\Qiankun-Pouch\src\services\tabStatusManager.ts                        | TypeScript     |        186 |         75 |         44 |        305 |
| d:\mydev\Qiankun-Pouch\src\services\tagService.ts                              | TypeScript     |        446 |        171 |         95 |        712 |
| d:\mydev\Qiankun-Pouch\src\styles\globals.css                                  | PostCSS        |        439 |         38 |         89 |        566 |
| d:\mydev\Qiankun-Pouch\src\styles\responsive.css                               | PostCSS        |        335 |         21 |         95 |        451 |
| d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.html                  | HTML           |         20 |          0 |          1 |         21 |
| d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.tsx                   | TypeScript JSX |        196 |          4 |         15 |        215 |
| d:\mydev\Qiankun-Pouch\src\types\ai.ts                                         | TypeScript     |        191 |         70 |         28 |        289 |
| d:\mydev\Qiankun-Pouch\src\types\import-export.ts                              | TypeScript     |        129 |         22 |         22 |        173 |
| d:\mydev\Qiankun-Pouch\src\types\index.ts                                      | TypeScript     |        337 |         33 |         42 |        412 |
| d:\mydev\Qiankun-Pouch\src\types\layout.ts                                     | TypeScript     |         91 |         38 |         15 |        144 |
| d:\mydev\Qiankun-Pouch\src\types\messages.ts                                   | TypeScript     |        330 |         34 |         59 |        423 |
| d:\mydev\Qiankun-Pouch\src\types\shadcn.ts                                     | TypeScript     |        185 |         52 |         62 |        299 |
| d:\mydev\Qiankun-Pouch\src\types\validation.ts                                 | TypeScript     |         40 |          9 |          8 |         57 |
| d:\mydev\Qiankun-Pouch\src\utils\bookmarkSortUtils.ts                          | TypeScript     |        113 |         36 |         20 |        169 |
| d:\mydev\Qiankun-Pouch\src\utils\chromeStorage.ts                              | TypeScript     |        427 |        117 |         65 |        609 |
| d:\mydev\Qiankun-Pouch\src\utils\chromeTestUtils.ts                            | TypeScript     |        176 |         31 |         16 |        223 |
| d:\mydev\Qiankun-Pouch\src\utils\colorUtils.ts                                 | TypeScript     |        290 |        134 |         69 |        493 |
| d:\mydev\Qiankun-Pouch\src\utils\dataMigration.ts                              | TypeScript     |        270 |         79 |         56 |        405 |
| d:\mydev\Qiankun-Pouch\src\utils\debounce.ts                                   | TypeScript     |         80 |         37 |         17 |        134 |
| d:\mydev\Qiankun-Pouch\src\utils\errorHandler.ts                               | TypeScript     |         60 |         27 |         10 |         97 |
| d:\mydev\Qiankun-Pouch\src\utils\errorHandler.tsx                              | TypeScript JSX |        254 |         89 |         50 |        393 |
| d:\mydev\Qiankun-Pouch\src\utils\fallbackStorage.ts                            | TypeScript     |        263 |         98 |         67 |        428 |
| d:\mydev\Qiankun-Pouch\src\utils\indexedDB.ts                                  | TypeScript     |        693 |        208 |        191 |      1,092 |
| d:\mydev\Qiankun-Pouch\src\utils\layoutStability.ts                            | TypeScript     |        213 |         45 |         53 |        311 |
| d:\mydev\Qiankun-Pouch\src\utils\layoutUtils.ts                                | TypeScript     |        233 |         98 |         48 |        379 |
| d:\mydev\Qiankun-Pouch\src\utils\messaging.ts                                  | TypeScript     |        252 |         66 |         54 |        372 |
| d:\mydev\Qiankun-Pouch\src\utils\modelFactory.ts                               | TypeScript     |        195 |        111 |         47 |        353 |
| d:\mydev\Qiankun-Pouch\src\utils\performance.ts                                | TypeScript     |        258 |         69 |         65 |        392 |
| d:\mydev\Qiankun-Pouch\src\utils\searchUtils.ts                                | TypeScript     |        328 |        118 |         72 |        518 |
| d:\mydev\Qiankun-Pouch\src\utils\serialization.ts                              | TypeScript     |        237 |         97 |         34 |        368 |
| d:\mydev\Qiankun-Pouch\src\utils\tagUtils.ts                                   | TypeScript     |        289 |        109 |         53 |        451 |
| d:\mydev\Qiankun-Pouch\src\utils\textUtils.ts                                  | TypeScript     |        232 |         73 |         61 |        366 |
| d:\mydev\Qiankun-Pouch\src\utils\validation.ts                                 | TypeScript     |        274 |        114 |         62 |        450 |
| d:\mydev\Qiankun-Pouch\src\utils\virtualScroll.ts                              | TypeScript     |        287 |         80 |         57 |        424 |
| d:\mydev\Qiankun-Pouch\src\workers\DataProcessingWorker.ts                     | TypeScript     |        410 |        152 |        104 |        666 |
| Total                                                                          |                |     56,117 |      9,955 |      8,302 |     74,374 |
+--------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+