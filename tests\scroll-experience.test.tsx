// 滚动体验测试组件
// 用于测试收藏管理页面的滚动修复效果

import React, { useState, useEffect } from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import VirtualBookmarkList from '../src/components/VirtualBookmarkList'
import type { Bookmark } from '../src/types'

// 模拟收藏数据生成器
const generateMockBookmarks = (count: number): Bookmark[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `bookmark-${index}`,
    title: `测试收藏 ${index + 1}`,
    url: `https://example.com/bookmark-${index}`,
    description: `这是第 ${index + 1} 个测试收藏的描述信息，用于测试滚动体验。`,
    category: index % 3 === 0 ? '技术' : index % 3 === 1 ? '生活' : '学习',
    tags: [`标签${index % 5}`, `类型${index % 3}`],
    favicon: `https://example.com/favicon-${index}.ico`,
    createdAt: new Date(Date.now() - index * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - index * 43200000).toISOString()
  }))
}

// 滚动性能测试组件
const ScrollPerformanceTest: React.FC = () => {
  const [bookmarks] = useState(() => generateMockBookmarks(1000))
  const [viewMode, setViewMode] = useState<'card' | 'compact' | 'row'>('card')
  const [scrollMetrics, setScrollMetrics] = useState({
    scrollEvents: 0,
    renderCount: 0,
    lastScrollTime: 0
  })

  const handleEdit = vi.fn()
  const handleDelete = vi.fn()
  const handleClick = vi.fn()

  // 监控滚动性能
  useEffect(() => {
    const container = document.querySelector('.virtual-scroll-container')
    if (!container) return

    let scrollEventCount = 0
    let lastScrollTime = 0

    const handleScroll = () => {
      scrollEventCount++
      lastScrollTime = performance.now()
      
      // 节流更新指标
      setTimeout(() => {
        setScrollMetrics(prev => ({
          ...prev,
          scrollEvents: scrollEventCount,
          lastScrollTime
        }))
      }, 100)
    }

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <div className="scroll-test-container p-4">
      <div className="test-controls mb-4 space-y-2">
        <h2 className="text-xl font-bold">滚动体验测试</h2>
        
        {/* 视图模式切换 */}
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('card')}
            className={`px-3 py-1 rounded ${viewMode === 'card' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            卡片视图
          </button>
          <button
            onClick={() => setViewMode('compact')}
            className={`px-3 py-1 rounded ${viewMode === 'compact' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            紧凑视图
          </button>
          <button
            onClick={() => setViewMode('row')}
            className={`px-3 py-1 rounded ${viewMode === 'row' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            列表视图
          </button>
        </div>

        {/* 性能指标 */}
        <div className="performance-metrics bg-gray-100 p-2 rounded text-sm">
          <div>滚动事件数: {scrollMetrics.scrollEvents}</div>
          <div>渲染次数: {scrollMetrics.renderCount}</div>
          <div>最后滚动时间: {scrollMetrics.lastScrollTime.toFixed(2)}ms</div>
        </div>
      </div>

      {/* 虚拟滚动列表 */}
      <div className="test-scroll-area border rounded">
        <VirtualBookmarkList
          bookmarks={bookmarks}
          viewMode={viewMode}
          containerHeight={600}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onClick={handleClick}
        />
      </div>

      {/* 测试说明 */}
      <div className="test-instructions mt-4 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">测试说明：</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>测试数据：1000个收藏项目</li>
          <li>滚动时观察是否有回弹现象</li>
          <li>检查内容是否重复显示</li>
          <li>验证不同视图模式下的滚动流畅度</li>
          <li>监控滚动事件频率和性能指标</li>
        </ul>
      </div>
    </div>
  )
}

describe('滚动体验测试', () => {
  beforeEach(() => {
    // 模拟浏览器环境
    Object.defineProperty(window, 'ResizeObserver', {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      })),
    })

    // 模拟 IntersectionObserver
    Object.defineProperty(window, 'IntersectionObserver', {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      })),
    })
  })

  it('应该正确渲染虚拟滚动列表', async () => {
    const mockBookmarks = generateMockBookmarks(100)
    
    render(
      <VirtualBookmarkList
        bookmarks={mockBookmarks}
        viewMode="card"
        containerHeight={400}
        onEdit={vi.fn()}
        onDelete={vi.fn()}
        onClick={vi.fn()}
      />
    )

    // 验证容器存在
    const container = screen.getByRole('generic', { hidden: true })
    expect(container).toHaveClass('virtual-scroll-container')
  })

  it('应该在不同视图模式下正常工作', async () => {
    const mockBookmarks = generateMockBookmarks(50)
    const viewModes: Array<'card' | 'compact' | 'row'> = ['card', 'compact', 'row']

    for (const viewMode of viewModes) {
      const { rerender } = render(
        <VirtualBookmarkList
          bookmarks={mockBookmarks}
          viewMode={viewMode}
          containerHeight={400}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onClick={vi.fn()}
        />
      )

      // 验证视图模式切换不会导致错误
      await waitFor(() => {
        const container = screen.getByRole('generic', { hidden: true })
        expect(container).toBeInTheDocument()
      })

      rerender(
        <VirtualBookmarkList
          bookmarks={mockBookmarks}
          viewMode={viewMode}
          containerHeight={400}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onClick={vi.fn()}
        />
      )
    }
  })

  it('应该正确处理滚动事件', async () => {
    const mockBookmarks = generateMockBookmarks(200)
    
    render(
      <VirtualBookmarkList
        bookmarks={mockBookmarks}
        viewMode="card"
        containerHeight={400}
        onEdit={vi.fn()}
        onDelete={vi.fn()}
        onClick={vi.fn()}
      />
    )

    const container = document.querySelector('.virtual-scroll-container')
    expect(container).toBeInTheDocument()

    // 模拟滚动事件
    if (container) {
      fireEvent.scroll(container, { target: { scrollTop: 100 } })
      fireEvent.scroll(container, { target: { scrollTop: 200 } })
      fireEvent.scroll(container, { target: { scrollTop: 300 } })

      // 验证滚动不会导致错误
      await waitFor(() => {
        expect(container).toBeInTheDocument()
      })
    }
  })

  it('应该优化大量数据的渲染性能', async () => {
    const mockBookmarks = generateMockBookmarks(1000)
    const startTime = performance.now()
    
    render(
      <VirtualBookmarkList
        bookmarks={mockBookmarks}
        viewMode="card"
        containerHeight={400}
        onEdit={vi.fn()}
        onDelete={vi.fn()}
        onClick={vi.fn()}
      />
    )

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // 验证渲染时间在合理范围内（小于100ms）
    expect(renderTime).toBeLessThan(100)
  })
})

export default ScrollPerformanceTest
