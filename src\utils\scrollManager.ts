/**
 * 滚动状态管理工具
 * 统一管理页面滚动状态，避免多个组件同时操作 document.body.style.overflow 导致冲突
 * 
 * 功能特性：
 * - 引用计数机制，确保只有在所有锁定都释放后才恢复滚动
 * - 自动清理机制，防止内存泄漏
 * - 错误恢复机制，确保滚动状态不会永久锁定
 * - 调试模式，便于问题排查
 */

import { useEffect, useRef, useCallback } from 'react'

// 滚动锁定原因枚举
export enum ScrollLockReason {
  MODAL = 'modal',
  DIALOG = 'dialog',
  DRAWER = 'drawer',
  LOADING = 'loading',
  CUSTOM = 'custom'
}

// 滚动锁定记录
interface ScrollLockRecord {
  id: string
  reason: ScrollLockReason
  timestamp: number
  component?: string
}

// 滚动管理器类
class ScrollManager {
  private locks: Map<string, ScrollLockRecord> = new Map()
  private originalOverflow: string = ''
  private isLocked: boolean = false
  private debugMode: boolean = false
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor() {
    // 记录初始滚动状态
    if (typeof document !== 'undefined') {
      this.originalOverflow = document.body.style.overflow || 'unset'
    }

    // 设置自动清理定时器（每30秒检查一次）
    this.setupCleanupTimer()

    // 页面卸载时强制恢复滚动
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', this.forceUnlockAll.bind(this))
    }
  }

  /**
   * 启用调试模式
   */
  enableDebug() {
    this.debugMode = true
  }

  /**
   * 禁用调试模式
   */
  disableDebug() {
    this.debugMode = false
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any) {
    if (this.debugMode) {
      console.log(`[ScrollManager] ${message}`, data || '')
    }
  }

  /**
   * 锁定滚动
   * @param id 锁定ID，用于后续释放
   * @param reason 锁定原因
   * @param component 组件名称（可选）
   */
  lock(id: string, reason: ScrollLockReason = ScrollLockReason.CUSTOM, component?: string): void {
    if (!id) {
      console.warn('[ScrollManager] 锁定ID不能为空')
      return
    }

    // 记录锁定
    const record: ScrollLockRecord = {
      id,
      reason,
      timestamp: Date.now(),
      component
    }

    this.locks.set(id, record)
    this.log(`锁定滚动: ${id}`, record)

    // 如果是第一次锁定，设置 overflow: hidden
    if (!this.isLocked) {
      if (typeof document !== 'undefined') {
        this.originalOverflow = document.body.style.overflow || 'unset'
        document.body.style.overflow = 'hidden'
        this.isLocked = true
        this.log('应用滚动锁定', { originalOverflow: this.originalOverflow })
      }
    }
  }

  /**
   * 释放滚动锁定
   * @param id 锁定ID
   */
  unlock(id: string): void {
    if (!id) {
      console.warn('[ScrollManager] 解锁ID不能为空')
      return
    }

    const record = this.locks.get(id)
    if (record) {
      this.locks.delete(id)
      this.log(`释放滚动锁定: ${id}`, record)
    } else {
      this.log(`尝试释放不存在的锁定: ${id}`)
    }

    // 如果没有任何锁定，恢复滚动
    if (this.locks.size === 0 && this.isLocked) {
      this.restoreScroll()
    }
  }

  /**
   * 强制释放所有锁定
   */
  forceUnlockAll(): void {
    this.log('强制释放所有滚动锁定', { locksCount: this.locks.size })
    this.locks.clear()
    this.restoreScroll()
  }

  /**
   * 恢复滚动状态
   */
  private restoreScroll(): void {
    if (typeof document !== 'undefined' && this.isLocked) {
      document.body.style.overflow = this.originalOverflow
      this.isLocked = false
      this.log('恢复滚动状态', { overflow: this.originalOverflow })
    }
  }

  /**
   * 获取当前锁定状态
   */
  getStatus() {
    return {
      isLocked: this.isLocked,
      locksCount: this.locks.size,
      locks: Array.from(this.locks.values()),
      originalOverflow: this.originalOverflow
    }
  }

  /**
   * 检查是否有特定原因的锁定
   */
  hasLockByReason(reason: ScrollLockReason): boolean {
    return Array.from(this.locks.values()).some(lock => lock.reason === reason)
  }

  /**
   * 设置自动清理定时器
   */
  private setupCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredLocks()
    }, 30000) // 每30秒检查一次
  }

  /**
   * 清理过期的锁定（超过5分钟的锁定被认为是异常的）
   */
  private cleanupExpiredLocks(): void {
    const now = Date.now()
    const expiredThreshold = 5 * 60 * 1000 // 5分钟

    const expiredLocks: string[] = []
    
    this.locks.forEach((record, id) => {
      if (now - record.timestamp > expiredThreshold) {
        expiredLocks.push(id)
      }
    })

    if (expiredLocks.length > 0) {
      this.log('清理过期的滚动锁定', expiredLocks)
      expiredLocks.forEach(id => this.unlock(id))
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }

    this.forceUnlockAll()

    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', this.forceUnlockAll.bind(this))
    }
  }
}

// 全局滚动管理器实例
const scrollManager = new ScrollManager()

// 开发环境下启用调试模式
if (process.env.NODE_ENV === 'development') {
  scrollManager.enableDebug()
}

/**
 * 滚动锁定 Hook
 * 在组件挂载时锁定滚动，卸载时自动释放
 */
export function useScrollLock(
  enabled: boolean = true,
  reason: ScrollLockReason = ScrollLockReason.CUSTOM,
  component?: string
) {
  const lockIdRef = useRef<string>()

  // 生成唯一的锁定ID
  const generateLockId = useCallback(() => {
    return `${component || 'unknown'}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [component])

  // 锁定滚动
  const lock = useCallback(() => {
    if (!lockIdRef.current) {
      lockIdRef.current = generateLockId()
    }
    scrollManager.lock(lockIdRef.current, reason, component)
  }, [reason, component, generateLockId])

  // 释放锁定
  const unlock = useCallback(() => {
    if (lockIdRef.current) {
      scrollManager.unlock(lockIdRef.current)
      lockIdRef.current = undefined
    }
  }, [])

  // 根据 enabled 状态自动锁定/释放
  useEffect(() => {
    if (enabled) {
      lock()
    } else {
      unlock()
    }

    // 组件卸载时确保释放锁定
    return unlock
  }, [enabled, lock, unlock])

  return {
    lock,
    unlock,
    lockId: lockIdRef.current
  }
}

/**
 * 模态框滚动锁定 Hook
 * 专门为模态框组件设计的滚动锁定
 */
export function useModalScrollLock(isOpen: boolean, componentName?: string) {
  return useScrollLock(isOpen, ScrollLockReason.MODAL, componentName)
}

/**
 * 对话框滚动锁定 Hook
 * 专门为对话框组件设计的滚动锁定
 */
export function useDialogScrollLock(isOpen: boolean, componentName?: string) {
  return useScrollLock(isOpen, ScrollLockReason.DIALOG, componentName)
}

// 导出滚动管理器实例（用于调试和高级用法）
export { scrollManager }

// 导出类型
export type { ScrollLockRecord }
