/**
 * 页面滚动状态监控工具
 * 用于生产环境中检测和自动恢复滚动问题
 * 
 * 创建日期：2025-08-21
 * 用途：预防和监控页面滚动失效问题
 */

interface ScrollIssueReport {
  timestamp: number
  bodyOverflow: string
  pageHeight: number
  viewportHeight: number
  scrollPosition: number
  canScroll: boolean
  userAgent: string
  url: string
}

interface ScrollMonitorConfig {
  /** 检查间隔（毫秒） */
  checkInterval: number
  /** 是否启用自动恢复 */
  autoRecover: boolean
  /** 是否启用日志记录 */
  enableLogging: boolean
  /** 问题报告回调 */
  onIssueDetected?: (report: ScrollIssueReport) => void
  /** 自动恢复回调 */
  onAutoRecover?: (report: ScrollIssueReport) => void
}

class ScrollMonitor {
  private config: ScrollMonitorConfig
  private monitorTimer: NodeJS.Timeout | null = null
  private lastScrollPosition: number = 0
  private scrollStuckCount: number = 0
  private isMonitoring: boolean = false

  constructor(config: Partial<ScrollMonitorConfig> = {}) {
    this.config = {
      checkInterval: 5000, // 每5秒检查一次
      autoRecover: true,
      enableLogging: false,
      ...config
    }
  }

  /**
   * 开始监控
   */
  start(): void {
    if (this.isMonitoring) {
      return
    }

    this.isMonitoring = true
    this.log('开始页面滚动监控')

    // 立即执行一次检查
    this.checkScrollStatus()

    // 设置定时检查
    this.monitorTimer = setInterval(() => {
      this.checkScrollStatus()
    }, this.config.checkInterval)

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))

    // 监听窗口焦点变化
    window.addEventListener('focus', this.handleWindowFocus.bind(this))
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false
    this.log('停止页面滚动监控')

    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
      this.monitorTimer = null
    }

    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    window.removeEventListener('focus', this.handleWindowFocus.bind(this))
  }

  /**
   * 检查滚动状态
   */
  private checkScrollStatus(): void {
    const report = this.generateReport()
    const hasIssue = this.detectScrollIssue(report)

    if (hasIssue) {
      this.handleScrollIssue(report)
    } else {
      // 重置计数器
      this.scrollStuckCount = 0
    }

    // 更新最后滚动位置
    this.lastScrollPosition = report.scrollPosition
  }

  /**
   * 生成状态报告
   */
  private generateReport(): ScrollIssueReport {
    const bodyOverflow = document.body.style.overflow || getComputedStyle(document.body).overflow
    const pageHeight = Math.max(
      document.body.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.clientHeight,
      document.documentElement.scrollHeight,
      document.documentElement.offsetHeight
    )
    const viewportHeight = window.innerHeight
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop

    return {
      timestamp: Date.now(),
      bodyOverflow,
      pageHeight,
      viewportHeight,
      scrollPosition,
      canScroll: this.testScrollability(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  /**
   * 测试页面是否可以滚动
   */
  private testScrollability(): boolean {
    const originalPosition = window.pageYOffset
    
    // 尝试微小的滚动
    window.scrollBy(0, 1)
    const newPosition = window.pageYOffset
    
    // 恢复原位置
    window.scrollTo(0, originalPosition)
    
    return newPosition !== originalPosition
  }

  /**
   * 检测是否存在滚动问题
   */
  private detectScrollIssue(report: ScrollIssueReport): boolean {
    // 检查1：body overflow 被设置为 hidden
    if (report.bodyOverflow === 'hidden') {
      this.log('检测到问题：body overflow 被设置为 hidden', report)
      return true
    }

    // 检查2：页面高度大于视口但无法滚动
    if (report.pageHeight > report.viewportHeight && !report.canScroll) {
      this.log('检测到问题：页面应该可滚动但无法滚动', report)
      return true
    }

    // 检查3：滚动位置长时间未变化（可能卡住了）
    if (report.scrollPosition === this.lastScrollPosition) {
      this.scrollStuckCount++
      if (this.scrollStuckCount > 3) { // 连续3次检查位置未变
        this.log('检测到问题：滚动位置长时间未变化', report)
        return true
      }
    }

    return false
  }

  /**
   * 处理滚动问题
   */
  private handleScrollIssue(report: ScrollIssueReport): void {
    this.log('发现滚动问题', report)

    // 调用问题报告回调
    if (this.config.onIssueDetected) {
      this.config.onIssueDetected(report)
    }

    // 自动恢复
    if (this.config.autoRecover) {
      this.attemptAutoRecover(report)
    }
  }

  /**
   * 尝试自动恢复
   */
  private attemptAutoRecover(report: ScrollIssueReport): void {
    this.log('尝试自动恢复滚动功能')

    try {
      // 方法1：重置 body overflow
      if (report.bodyOverflow === 'hidden') {
        document.body.style.overflow = 'unset'
        this.log('已重置 body overflow 为 unset')
      }

      // 方法2：强制释放所有滚动锁定（如果滚动管理器可用）
      if (typeof window !== 'undefined' && (window as any).scrollManager) {
        (window as any).scrollManager.forceUnlockAll()
        this.log('已强制释放所有滚动锁定')
      }

      // 方法3：触发重新布局
      document.body.style.display = 'none'
      document.body.offsetHeight // 强制重排
      document.body.style.display = ''

      // 验证恢复效果
      setTimeout(() => {
        const newReport = this.generateReport()
        if (!this.detectScrollIssue(newReport)) {
          this.log('自动恢复成功')
          if (this.config.onAutoRecover) {
            this.config.onAutoRecover(newReport)
          }
        } else {
          this.log('自动恢复失败，可能需要手动干预')
        }
      }, 100)

    } catch (error) {
      this.log('自动恢复过程中发生错误', error)
    }
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (!document.hidden) {
      // 页面变为可见时检查滚动状态
      this.log('页面变为可见，检查滚动状态')
      setTimeout(() => {
        this.checkScrollStatus()
      }, 100)
    }
  }

  /**
   * 处理窗口焦点变化
   */
  private handleWindowFocus(): void {
    // 窗口获得焦点时检查滚动状态
    this.log('窗口获得焦点，检查滚动状态')
    setTimeout(() => {
      this.checkScrollStatus()
    }, 100)
  }

  /**
   * 日志记录
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      const timestamp = new Date().toISOString()
      console.log(`[ScrollMonitor ${timestamp}] ${message}`, data || '')
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      config: this.config,
      lastScrollPosition: this.lastScrollPosition,
      scrollStuckCount: this.scrollStuckCount
    }
  }

  /**
   * 手动触发检查
   */
  checkNow(): ScrollIssueReport {
    const report = this.generateReport()
    const hasIssue = this.detectScrollIssue(report)
    
    if (hasIssue) {
      this.handleScrollIssue(report)
    }
    
    return report
  }
}

// 创建全局监控实例
let globalScrollMonitor: ScrollMonitor | null = null

/**
 * 初始化滚动监控
 */
export function initScrollMonitor(config?: Partial<ScrollMonitorConfig>): ScrollMonitor {
  if (globalScrollMonitor) {
    globalScrollMonitor.stop()
  }

  globalScrollMonitor = new ScrollMonitor(config)
  
  // 在开发环境中自动启用日志
  if (process.env.NODE_ENV === 'development') {
    globalScrollMonitor = new ScrollMonitor({
      enableLogging: true,
      ...config
    })
  }

  return globalScrollMonitor
}

/**
 * 获取全局监控实例
 */
export function getScrollMonitor(): ScrollMonitor | null {
  return globalScrollMonitor
}

/**
 * 启动默认监控
 */
export function startDefaultMonitoring(): void {
  const monitor = initScrollMonitor({
    checkInterval: 10000, // 每10秒检查一次
    autoRecover: true,
    enableLogging: process.env.NODE_ENV === 'development',
    onIssueDetected: (report) => {
      // 可以在这里添加错误报告逻辑
      console.warn('检测到页面滚动问题', report)
    },
    onAutoRecover: (report) => {
      console.info('页面滚动问题已自动恢复', report)
    }
  })

  monitor.start()
}

/**
 * 停止监控
 */
export function stopScrollMonitoring(): void {
  if (globalScrollMonitor) {
    globalScrollMonitor.stop()
    globalScrollMonitor = null
  }
}

// 导出类型
export type { ScrollIssueReport, ScrollMonitorConfig }
export { ScrollMonitor }
