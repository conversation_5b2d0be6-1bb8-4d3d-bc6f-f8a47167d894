"filename", "language", "TypeScript", "TypeScript JSX", "HTML", "PostCSS", "comment", "blank", "total"
"d:\mydev\Qiankun-Pouch\src\background\messageHandler.ts", "TypeScript", 61, 0, 0, 0, 12, 8, 81
"d:\mydev\Qiankun-Pouch\src\components\AIIntegrationTab.tsx", "TypeScript JSX", 0, 129, 0, 0, 8, 13, 150
"d:\mydev\Qiankun-Pouch\src\components\AIRecommendations.tsx", "TypeScript JSX", 0, 553, 0, 0, 131, 92, 776
"d:\mydev\Qiankun-Pouch\src\components\BookmarkEditModal.tsx", "TypeScript JSX", 0, 252, 0, 0, 25, 31, 308
"d:\mydev\Qiankun-Pouch\src\components\CategoryCard.tsx", "TypeScript JSX", 0, 48, 0, 0, 2, 2, 52
"d:\mydev\Qiankun-Pouch\src\components\CategoryManagementTab.tsx", "TypeScript JSX", 0, -23, 0, 0, -4, -3, -30
"d:\mydev\Qiankun-Pouch\src\components\DefaultAIModelsTab.tsx", "TypeScript JSX", 0, 111, 0, 0, 11, 12, 134
"d:\mydev\Qiankun-Pouch\src\components\MCPSettingsTab.tsx", "TypeScript JSX", 0, 203, 0, 0, 13, 21, 237
"d:\mydev\Qiankun-Pouch\src\components\ManagementPageLayout.tsx", "TypeScript JSX", 0, 81, 0, 0, 25, 11, 117
"d:\mydev\Qiankun-Pouch\src\components\ModelSelector.tsx", "TypeScript JSX", 0, 3, 0, 0, 0, 0, 3
"d:\mydev\Qiankun-Pouch\src\components\SmartFolderSelector.tsx", "TypeScript JSX", 0, 274, 0, 0, 54, 34, 362
"d:\mydev\Qiankun-Pouch\src\components\SmartTagInput.tsx", "TypeScript JSX", 0, 296, 0, 0, 56, 31, 383
"d:\mydev\Qiankun-Pouch\src\components\TagCard.tsx", "TypeScript JSX", 0, 17, 0, 0, 0, 0, 17
"d:\mydev\Qiankun-Pouch\src\components\TagManagementTab.tsx", "TypeScript JSX", 0, -21, 0, 0, -4, -3, -28
"d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.html", "HTML", 0, 0, 13, 0, 0, 1, 14
"d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\examples\ManagementLayoutDemo.tsx", "TypeScript JSX", 0, 168, 0, 0, 4, 15, 187
"d:\mydev\Qiankun-Pouch\src\options\data\helpContent.ts", "TypeScript", 47, 0, 0, 0, 0, -47, 0
"d:\mydev\Qiankun-Pouch\src\popup\PopupApp.tsx", "TypeScript JSX", 0, 60, 0, 0, 4, 3, 67
"d:\mydev\Qiankun-Pouch\src\popup\components\DetailedBookmarkForm.tsx", "TypeScript JSX", 0, 252, 0, 0, 26, 29, 307
"d:\mydev\Qiankun-Pouch\src\popup\components\QuickBookmarkForm.tsx", "TypeScript JSX", 0, 209, 0, 0, 47, 22, 278
"d:\mydev\Qiankun-Pouch\src\popup\components\SmartRecognition.tsx", "TypeScript JSX", 0, 197, 0, 0, 41, 24, 262
"d:\mydev\Qiankun-Pouch\src\services\BookmarkImportExportService.ts", "TypeScript", 20, 0, 0, 0, -8, -12, 0
"d:\mydev\Qiankun-Pouch\src\services\aiChatService.ts", "TypeScript", 53, 0, 0, 0, 5, 9, 67
"d:\mydev\Qiankun-Pouch\src\services\aiIntegrationService.ts", "TypeScript", 22, 0, 0, 0, 13, 8, 43
"d:\mydev\Qiankun-Pouch\src\services\aiProviderService.ts", "TypeScript", 441, 0, 0, 0, 31, 25, 497
"d:\mydev\Qiankun-Pouch\src\services\aiRecommendationService.ts", "TypeScript", 470, 0, 0, 0, 122, 99, 691
"d:\mydev\Qiankun-Pouch\src\services\aiService.ts", "TypeScript", 651, 0, 0, 0, 151, 108, 910
"d:\mydev\Qiankun-Pouch\src\services\categoryService.ts", "TypeScript", 8, 0, 0, 0, 4, 1, 13
"d:\mydev\Qiankun-Pouch\src\services\defaultAIModelAPI.ts", "TypeScript", 9, 0, 0, 0, 0, 3, 12
"d:\mydev\Qiankun-Pouch\src\services\defaultAIModelService.ts", "TypeScript", 94, 0, 0, 0, 31, 20, 145
"d:\mydev\Qiankun-Pouch\src\services\mcpTestService.ts", "TypeScript", 208, 0, 0, 0, 59, 41, 308
"d:\mydev\Qiankun-Pouch\src\styles\globals.css", "PostCSS", 0, 0, 0, 92, 3, 21, 116
"d:\mydev\Qiankun-Pouch\src\styles\responsive.css", "PostCSS", 0, 0, 0, 41, 3, 8, 52
"d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.html", "HTML", 0, 0, 20, 0, 0, 1, 21
"d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.tsx", "TypeScript JSX", 0, 196, 0, 0, 4, 15, 215
"d:\mydev\Qiankun-Pouch\src\types\messages.ts", "TypeScript", 68, 0, 0, 0, 3, 6, 77
"d:\mydev\Qiankun-Pouch\src\utils\indexedDB.ts", "TypeScript", 119, 0, 0, 0, 39, 37, 195
"Total", "-", 2271, 3015, 33, 133, 912, 689, 7053