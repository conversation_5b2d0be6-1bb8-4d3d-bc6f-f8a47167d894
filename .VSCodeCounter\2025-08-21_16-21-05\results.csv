"filename", "language", "TypeScript", "TypeScript JSX", "HTML", "PostCSS", "comment", "blank", "total"
"d:\mydev\Qiankun-Pouch\src\background\index.ts", "TypeScript", 92, 0, 0, 0, 18, 22, 132
"d:\mydev\Qiankun-Pouch\src\background\messageHandler.ts", "TypeScript", 782, 0, 0, 0, 189, 136, 1107
"d:\mydev\Qiankun-Pouch\src\components\AIConfigPanel.tsx", "TypeScript JSX", 0, 411, 0, 0, 19, 45, 475
"d:\mydev\Qiankun-Pouch\src\components\AIIntegrationTab.tsx", "TypeScript JSX", 0, 834, 0, 0, 57, 88, 979
"d:\mydev\Qiankun-Pouch\src\components\AIRecommendations.tsx", "TypeScript JSX", 0, 553, 0, 0, 131, 92, 776
"d:\mydev\Qiankun-Pouch\src\components\AITextGenerator.tsx", "TypeScript JSX", 0, 270, 0, 0, 39, 30, 339
"d:\mydev\Qiankun-Pouch\src\components\AddBookmarkModal.tsx", "TypeScript JSX", 0, 551, 0, 0, 45, 48, 644
"d:\mydev\Qiankun-Pouch\src\components\BookmarkCompact.tsx", "TypeScript JSX", 0, 265, 0, 0, 39, 25, 329
"d:\mydev\Qiankun-Pouch\src\components\BookmarkEditModal.tsx", "TypeScript JSX", 0, 603, 0, 0, 53, 58, 714
"d:\mydev\Qiankun-Pouch\src\components\BookmarkRow.tsx", "TypeScript JSX", 0, 229, 0, 0, 35, 22, 286
"d:\mydev\Qiankun-Pouch\src\components\BookmarkSortSelector.tsx", "TypeScript JSX", 0, 45, 0, 0, 18, 9, 72
"d:\mydev\Qiankun-Pouch\src\components\BookmarkToolbar.tsx", "TypeScript JSX", 0, 227, 0, 0, 31, 31, 289
"d:\mydev\Qiankun-Pouch\src\components\BookmarksTab.tsx", "TypeScript JSX", 0, 424, 0, 0, 92, 58, 574
"d:\mydev\Qiankun-Pouch\src\components\CategoryCard.tsx", "TypeScript JSX", 0, 219, 0, 0, 40, 28, 287
"d:\mydev\Qiankun-Pouch\src\components\CategoryForm.tsx", "TypeScript JSX", 0, 294, 0, 0, 33, 33, 360
"d:\mydev\Qiankun-Pouch\src\components\CategoryList.tsx", "TypeScript JSX", 0, 114, 0, 0, 20, 14, 148
"d:\mydev\Qiankun-Pouch\src\components\CategoryManagementTab.tsx", "TypeScript JSX", 0, 208, 0, 0, 31, 36, 275
"d:\mydev\Qiankun-Pouch\src\components\CategoryModal.tsx", "TypeScript JSX", 0, 205, 0, 0, 32, 24, 261
"d:\mydev\Qiankun-Pouch\src\components\ConflictResolutionDialog.tsx", "TypeScript JSX", 0, 283, 0, 0, 19, 26, 328
"d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\BatchActionsPanel.tsx", "TypeScript JSX", 0, 34, 0, 0, 1, 4, 39
"d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\ConflictListPanel.tsx", "TypeScript JSX", 0, 74, 0, 0, 1, 8, 83
"d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\ManualEditForm.tsx", "TypeScript JSX", 0, 72, 0, 0, 1, 7, 80
"d:\mydev\Qiankun-Pouch\src\components\ConflictResolution\utils.ts", "TypeScript", 36, 0, 0, 0, 5, 4, 45
"d:\mydev\Qiankun-Pouch\src\components\DefaultAIModelsTab.tsx", "TypeScript JSX", 0, 520, 0, 0, 31, 50, 601
"d:\mydev\Qiankun-Pouch\src\components\DeleteConfirmModal.tsx", "TypeScript JSX", 0, 221, 0, 0, 25, 24, 270
"d:\mydev\Qiankun-Pouch\src\components\HelpTooltip.tsx", "TypeScript JSX", 0, 243, 0, 0, 27, 29, 299
"d:\mydev\Qiankun-Pouch\src\components\HelpTooltip\helpContent.ts", "TypeScript", 73, 0, 0, 0, 10, 4, 87
"d:\mydev\Qiankun-Pouch\src\components\HelpTooltip\useTooltip.ts", "TypeScript", 93, 0, 0, 0, 16, 18, 127
"d:\mydev\Qiankun-Pouch\src\components\ImportExportTab.tsx", "TypeScript JSX", 0, 719, 0, 0, 43, 78, 840
"d:\mydev\Qiankun-Pouch\src\components\LazyLoadWrapper.tsx", "TypeScript JSX", 0, 302, 0, 0, 31, 53, 386
"d:\mydev\Qiankun-Pouch\src\components\LoadingIndicator.tsx", "TypeScript JSX", 0, 86, 0, 0, 19, 13, 118
"d:\mydev\Qiankun-Pouch\src\components\MCPSettingsTab.tsx", "TypeScript JSX", 0, 884, 0, 0, 48, 82, 1014
"d:\mydev\Qiankun-Pouch\src\components\ManagementPageLayout.tsx", "TypeScript JSX", 0, 81, 0, 0, 25, 11, 117
"d:\mydev\Qiankun-Pouch\src\components\ModelSelector.tsx", "TypeScript JSX", 0, 335, 0, 0, 10, 21, 366
"d:\mydev\Qiankun-Pouch\src\components\NotionSyncTab.tsx", "TypeScript JSX", 0, 366, 0, 0, 18, 23, 407
"d:\mydev\Qiankun-Pouch\src\components\ObsidianIntegrationTab.tsx", "TypeScript JSX", 0, 667, 0, 0, 23, 52, 742
"d:\mydev\Qiankun-Pouch\src\components\OptionsPageErrorBoundary.tsx", "TypeScript JSX", 0, 238, 0, 0, 40, 36, 314
"d:\mydev\Qiankun-Pouch\src\components\SmartFolderSelector.tsx", "TypeScript JSX", 0, 274, 0, 0, 54, 34, 362
"d:\mydev\Qiankun-Pouch\src\components\SmartTagInput.tsx", "TypeScript JSX", 0, 296, 0, 0, 56, 31, 383
"d:\mydev\Qiankun-Pouch\src\components\SuperMarketTab.tsx", "TypeScript JSX", 0, 445, 0, 0, 23, 29, 497
"d:\mydev\Qiankun-Pouch\src\components\TagBatchActions.tsx", "TypeScript JSX", 0, 328, 0, 0, 26, 24, 378
"d:\mydev\Qiankun-Pouch\src\components\TagCard.tsx", "TypeScript JSX", 0, 209, 0, 0, 39, 30, 278
"d:\mydev\Qiankun-Pouch\src\components\TagCloud.tsx", "TypeScript JSX", 0, 149, 0, 0, 28, 20, 197
"d:\mydev\Qiankun-Pouch\src\components\TagColorPicker.tsx", "TypeScript JSX", 0, 232, 0, 0, 48, 23, 303
"d:\mydev\Qiankun-Pouch\src\components\TagForm.tsx", "TypeScript JSX", 0, 285, 0, 0, 66, 40, 391
"d:\mydev\Qiankun-Pouch\src\components\TagList.tsx", "TypeScript JSX", 0, 415, 0, 0, 55, 46, 516
"d:\mydev\Qiankun-Pouch\src\components\TagManagementTab.tsx", "TypeScript JSX", 0, 323, 0, 0, 44, 58, 425
"d:\mydev\Qiankun-Pouch\src\components\TagModal.tsx", "TypeScript JSX", 0, 212, 0, 0, 34, 25, 271
"d:\mydev\Qiankun-Pouch\src\components\TagsTab.tsx", "TypeScript JSX", 0, 133, 0, 0, 20, 18, 171
"d:\mydev\Qiankun-Pouch\src\components\Toast.tsx", "TypeScript JSX", 0, 119, 0, 0, 21, 17, 157
"d:\mydev\Qiankun-Pouch\src\components\ToastContainer.tsx", "TypeScript JSX", 0, 38, 0, 0, 8, 5, 51
"d:\mydev\Qiankun-Pouch\src\components\TruncatedTitle.tsx", "TypeScript JSX", 0, 138, 0, 0, 31, 27, 196
"d:\mydev\Qiankun-Pouch\src\components\ViewModeSelector.tsx", "TypeScript JSX", 0, 104, 0, 0, 15, 10, 129
"d:\mydev\Qiankun-Pouch\src\components\VirtualBookmarkList.tsx", "TypeScript JSX", 0, 276, 0, 0, 22, 24, 322
"d:\mydev\Qiankun-Pouch\src\components\VirtualScrollList.tsx", "TypeScript JSX", 0, 230, 0, 0, 36, 38, 304
"d:\mydev\Qiankun-Pouch\src\components\examples\AddBookmarkModalDemo.tsx", "TypeScript JSX", 0, 165, 0, 0, 17, 18, 200
"d:\mydev\Qiankun-Pouch\src\components\examples\AdvancedComponentsDemo.tsx", "TypeScript JSX", 0, 167, 0, 0, 9, 11, 187
"d:\mydev\Qiankun-Pouch\src\components\examples\BasicComponentsDemo.tsx", "TypeScript JSX", 0, 176, 0, 0, 8, 8, 192
"d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkCompactDemo.tsx", "TypeScript JSX", 0, 291, 0, 0, 10, 21, 322
"d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkEditModalDemo.tsx", "TypeScript JSX", 0, 108, 0, 0, 10, 13, 131
"d:\mydev\Qiankun-Pouch\src\components\examples\BookmarkRowDemo.tsx", "TypeScript JSX", 0, 149, 0, 0, 6, 11, 166
"d:\mydev\Qiankun-Pouch\src\components\examples\DeleteConfirmModalDemo.tsx", "TypeScript JSX", 0, 178, 0, 0, 14, 21, 213
"d:\mydev\Qiankun-Pouch\src\components\examples\PopupAppDemo.tsx", "TypeScript JSX", 0, 56, 0, 0, 9, 8, 73
"d:\mydev\Qiankun-Pouch\src\components\examples\VirtualBookmarkListDemo.tsx", "TypeScript JSX", 0, 202, 0, 0, 16, 22, 240
"d:\mydev\Qiankun-Pouch\src\components\test\AIGeneratorTest.tsx", "TypeScript JSX", 0, 361, 0, 0, 28, 30, 419
"d:\mydev\Qiankun-Pouch\src\components\test\AIModelChatTest.tsx", "TypeScript JSX", 0, 659, 0, 0, 35, 75, 769
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarkCompactTest.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarkEditModalTest.tsx", "TypeScript JSX", 0, 223, 0, 0, 12, 18, 253
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarkSortTest.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarkTagColorTest.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarkToolbarTest.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\components\test\BookmarksTabTestPage.tsx", "TypeScript JSX", 0, 233, 0, 0, 19, 18, 270
"d:\mydev\Qiankun-Pouch\src\components\test\CardImportTest.tsx", "TypeScript JSX", 0, 18, 0, 0, 1, 3, 22
"d:\mydev\Qiankun-Pouch\src\components\test\CloudAIServiceTest.tsx", "TypeScript JSX", 0, 814, 0, 0, 35, 85, 934
"d:\mydev\Qiankun-Pouch\src\components\test\DeleteConfirmModalTest.tsx", "TypeScript JSX", 0, 321, 0, 0, 20, 26, 367
"d:\mydev\Qiankun-Pouch\src\components\test\DeleteConfirmModalTestPage.tsx", "TypeScript JSX", 0, 418, 0, 0, 23, 30, 471
"d:\mydev\Qiankun-Pouch\src\components\test\LocalAIServiceTestPage.tsx", "TypeScript JSX", 0, 540, 0, 0, 27, 67, 634
"d:\mydev\Qiankun-Pouch\src\components\test\OtherComponentsTestPage.tsx", "TypeScript JSX", 0, 274, 0, 0, 9, 21, 304
"d:\mydev\Qiankun-Pouch\src\components\test\PopupAppTest.tsx", "TypeScript JSX", 0, 150, 0, 0, 18, 18, 186
"d:\mydev\Qiankun-Pouch\src\components\test\PopupAppTestPage.tsx", "TypeScript JSX", 0, 6, 0, 0, 5, 2, 13
"d:\mydev\Qiankun-Pouch\src\components\test\ShadcnModalTest.tsx", "TypeScript JSX", 0, 173, 0, 0, 9, 15, 197
"d:\mydev\Qiankun-Pouch\src\components\test\ShadcnTest.tsx", "TypeScript JSX", 0, 26, 0, 0, 7, 4, 37
"d:\mydev\Qiankun-Pouch\src\components\test\TestErrorBoundary.tsx", "TypeScript JSX", 0, 106, 0, 0, 7, 15, 128
"d:\mydev\Qiankun-Pouch\src\components\test\VirtualBookmarkListTest.tsx", "TypeScript JSX", 0, 352, 0, 0, 26, 35, 413
"d:\mydev\Qiankun-Pouch\src\components\test\VirtualBookmarkListTestPage.tsx", "TypeScript JSX", 0, 252, 0, 0, 12, 12, 276
"d:\mydev\Qiankun-Pouch\src\components\ui\alert-dialog.tsx", "TypeScript JSX", 0, 126, 0, 0, 0, 14, 140
"d:\mydev\Qiankun-Pouch\src\components\ui\alert.tsx", "TypeScript JSX", 0, 53, 0, 0, 0, 7, 60
"d:\mydev\Qiankun-Pouch\src\components\ui\badge.tsx", "TypeScript JSX", 0, 31, 0, 0, 0, 6, 37
"d:\mydev\Qiankun-Pouch\src\components\ui\button.tsx", "TypeScript JSX", 0, 51, 0, 0, 0, 6, 57
"d:\mydev\Qiankun-Pouch\src\components\ui\card.tsx", "TypeScript JSX", 0, 71, 0, 0, 0, 9, 80
"d:\mydev\Qiankun-Pouch\src\components\ui\checkbox.tsx", "TypeScript JSX", 0, 25, 0, 0, 0, 4, 29
"d:\mydev\Qiankun-Pouch\src\components\ui\dialog.tsx", "TypeScript JSX", 0, 108, 0, 0, 0, 13, 121
"d:\mydev\Qiankun-Pouch\src\components\ui\dropdown-menu.tsx", "TypeScript JSX", 0, 181, 0, 0, 0, 18, 199
"d:\mydev\Qiankun-Pouch\src\components\ui\form.tsx", "TypeScript JSX", 0, 152, 0, 0, 0, 25, 177
"d:\mydev\Qiankun-Pouch\src\components\ui\input.tsx", "TypeScript JSX", 0, 19, 0, 0, 0, 4, 23
"d:\mydev\Qiankun-Pouch\src\components\ui\label.tsx", "TypeScript JSX", 0, 20, 0, 0, 0, 5, 25
"d:\mydev\Qiankun-Pouch\src\components\ui\progress.tsx", "TypeScript JSX", 0, 23, 0, 0, 0, 4, 27
"d:\mydev\Qiankun-Pouch\src\components\ui\select.tsx", "TypeScript JSX", 0, 145, 0, 0, 0, 13, 158
"d:\mydev\Qiankun-Pouch\src\components\ui\separator.tsx", "TypeScript JSX", 0, 26, 0, 0, 0, 4, 30
"d:\mydev\Qiankun-Pouch\src\components\ui\switch.tsx", "TypeScript JSX", 0, 24, 0, 0, 0, 4, 28
"d:\mydev\Qiankun-Pouch\src\components\ui\tabs.tsx", "TypeScript JSX", 0, 47, 0, 0, 0, 6, 53
"d:\mydev\Qiankun-Pouch\src\components\ui\textarea.tsx", "TypeScript JSX", 0, 20, 0, 0, 0, 4, 24
"d:\mydev\Qiankun-Pouch\src\components\ui\tooltip.tsx", "TypeScript JSX", 0, 22, 0, 0, 0, 7, 29
"d:\mydev\Qiankun-Pouch\src\constants\index.ts", "TypeScript", 1, 0, 0, 0, 2, 1, 4
"d:\mydev\Qiankun-Pouch\src\constants\shadcn.ts", "TypeScript", 250, 0, 0, 0, 19, 25, 294
"d:\mydev\Qiankun-Pouch\src\content\index.ts", "TypeScript", 78, 0, 0, 0, 12, 20, 110
"d:\mydev\Qiankun-Pouch\src\content\style.css", "PostCSS", 0, 0, 0, 72, 7, 11, 90
"d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModal.html", "HTML", 0, 0, 20, 0, 0, 1, 21
"d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModalTest.html", "HTML", 0, 0, 26, 0, 0, 1, 27
"d:\mydev\Qiankun-Pouch\src\demo\addBookmarkModalTest.tsx", "TypeScript JSX", 0, 12, 0, 0, 2, 3, 17
"d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.html", "HTML", 0, 0, 13, 0, 0, 1, 14
"d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.tsx", "TypeScript JSX", 0, 10, 0, 0, 1, 3, 14
"d:\mydev\Qiankun-Pouch\src\examples\BookmarkSortDemo.tsx", "TypeScript JSX", 0, 279, 0, 0, 11, 16, 306
"d:\mydev\Qiankun-Pouch\src\examples\BookmarkTagColorDemo.tsx", "TypeScript JSX", 0, 233, 0, 0, 12, 13, 258
"d:\mydev\Qiankun-Pouch\src\examples\BookmarkToolbarDemo.tsx", "TypeScript JSX", 0, 242, 0, 0, 17, 36, 295
"d:\mydev\Qiankun-Pouch\src\examples\CategoryCardDemo.tsx", "TypeScript JSX", 0, 147, 0, 0, 2, 12, 161
"d:\mydev\Qiankun-Pouch\src\examples\CategoryFormDemo.tsx", "TypeScript JSX", 0, 208, 0, 0, 13, 27, 248
"d:\mydev\Qiankun-Pouch\src\examples\CategoryModalDemo.tsx", "TypeScript JSX", 0, 203, 0, 0, 20, 22, 245
"d:\mydev\Qiankun-Pouch\src\examples\ManagementLayoutDemo.tsx", "TypeScript JSX", 0, 168, 0, 0, 4, 15, 187
"d:\mydev\Qiankun-Pouch\src\examples\TagCardDemo.tsx", "TypeScript JSX", 0, 267, 0, 0, 22, 17, 306
"d:\mydev\Qiankun-Pouch\src\examples\TagColorPickerDemo.tsx", "TypeScript JSX", 0, 163, 0, 0, 12, 11, 186
"d:\mydev\Qiankun-Pouch\src\examples\TagFormDemo.tsx", "TypeScript JSX", 0, 238, 0, 0, 25, 21, 284
"d:\mydev\Qiankun-Pouch\src\examples\TagListDemo.tsx", "TypeScript JSX", 0, 184, 0, 0, 18, 23, 225
"d:\mydev\Qiankun-Pouch\src\examples\TagManagementTabDemo.tsx", "TypeScript JSX", 0, 14, 0, 0, 5, 3, 22
"d:\mydev\Qiankun-Pouch\src\examples\TagModalDemo.tsx", "TypeScript JSX", 0, 133, 0, 0, 18, 18, 169
"d:\mydev\Qiankun-Pouch\src\examples\ViewModeSelectorExample.tsx", "TypeScript JSX", 0, 99, 0, 0, 8, 11, 118
"d:\mydev\Qiankun-Pouch\src\hooks\useAdvancedSearch.ts", "TypeScript", 249, 0, 0, 0, 44, 48, 341
"d:\mydev\Qiankun-Pouch\src\hooks\useConflictResolution.ts", "TypeScript", 143, 0, 0, 0, 18, 23, 184
"d:\mydev\Qiankun-Pouch\src\hooks\useLoadingState.ts", "TypeScript", 63, 0, 0, 0, 17, 11, 91
"d:\mydev\Qiankun-Pouch\src\hooks\useShadcn.ts", "TypeScript", 308, 0, 0, 0, 16, 61, 385
"d:\mydev\Qiankun-Pouch\src\hooks\useTagColors.ts", "TypeScript", 48, 0, 0, 0, 26, 12, 86
"d:\mydev\Qiankun-Pouch\src\hooks\useToast.ts", "TypeScript", 72, 0, 0, 0, 21, 15, 108
"d:\mydev\Qiankun-Pouch\src\hooks\useViewMode.ts", "TypeScript", 44, 0, 0, 0, 12, 9, 65
"d:\mydev\Qiankun-Pouch\src\lib\shadcn-config.ts", "TypeScript", 227, 0, 0, 0, 39, 34, 300
"d:\mydev\Qiankun-Pouch\src\lib\utils.ts", "TypeScript", 5, 0, 0, 0, 0, 2, 7
"d:\mydev\Qiankun-Pouch\src\options\OptionsApp.tsx", "TypeScript JSX", 0, 61, 0, 0, 13, 13, 87
"d:\mydev\Qiankun-Pouch\src\options\components\AIAssistantTab.tsx", "TypeScript JSX", 0, 66, 0, 0, 2, 6, 74
"d:\mydev\Qiankun-Pouch\src\options\components\AboutTab.tsx", "TypeScript JSX", 0, 260, 0, 0, 19, 25, 304
"d:\mydev\Qiankun-Pouch\src\options\components\AppHeader.tsx", "TypeScript JSX", 0, 33, 0, 0, 3, 4, 40
"d:\mydev\Qiankun-Pouch\src\options\components\ErrorState.tsx", "TypeScript JSX", 0, 62, 0, 0, 1, 3, 66
"d:\mydev\Qiankun-Pouch\src\options\components\HelpCenterTab.tsx", "TypeScript JSX", 0, 469, 0, 0, 33, 38, 540
"d:\mydev\Qiankun-Pouch\src\options\components\HelpSearchBox.tsx", "TypeScript JSX", 0, 217, 0, 0, 26, 32, 275
"d:\mydev\Qiankun-Pouch\src\options\components\LoadingState.tsx", "TypeScript JSX", 0, 22, 0, 0, 1, 3, 26
"d:\mydev\Qiankun-Pouch\src\options\components\NavigationSidebar.tsx", "TypeScript JSX", 0, 118, 0, 0, 2, 5, 125
"d:\mydev\Qiankun-Pouch\src\options\components\PageErrorBoundary.tsx", "TypeScript JSX", 0, 231, 0, 0, 36, 39, 306
"d:\mydev\Qiankun-Pouch\src\options\components\SettingsTab.tsx", "TypeScript JSX", 0, 84, 0, 0, 2, 6, 92
"d:\mydev\Qiankun-Pouch\src\options\components\SyncTab.tsx", "TypeScript JSX", 0, 77, 0, 0, 2, 6, 85
"d:\mydev\Qiankun-Pouch\src\options\components\TabContentRenderer.tsx", "TypeScript JSX", 0, 101, 0, 0, 5, 8, 114
"d:\mydev\Qiankun-Pouch\src\options\components\ThemeToggle.tsx", "TypeScript JSX", 0, 129, 0, 0, 8, 11, 148
"d:\mydev\Qiankun-Pouch\src\options\constants\tabsConfig.ts", "TypeScript", 66, 0, 0, 0, 10, 7, 83
"d:\mydev\Qiankun-Pouch\src\options\data\aboutInfo.ts", "TypeScript", 52, 0, 0, 0, 5, 3, 60
"d:\mydev\Qiankun-Pouch\src\options\data\helpContent.ts", "TypeScript", 275, 0, 0, 0, 10, 9, 294
"d:\mydev\Qiankun-Pouch\src\options\hooks\useAppInitialization.ts", "TypeScript", 40, 0, 0, 0, 7, 8, 55
"d:\mydev\Qiankun-Pouch\src\options\hooks\useCache.ts", "TypeScript", 277, 0, 0, 0, 35, 64, 376
"d:\mydev\Qiankun-Pouch\src\options\hooks\useLazyLoad.ts", "TypeScript", 158, 0, 0, 0, 18, 26, 202
"d:\mydev\Qiankun-Pouch\src\options\hooks\useResponsive.ts", "TypeScript", 119, 0, 0, 0, 34, 23, 176
"d:\mydev\Qiankun-Pouch\src\options\hooks\useTabNavigation.ts", "TypeScript", 77, 0, 0, 0, 11, 14, 102
"d:\mydev\Qiankun-Pouch\src\options\hooks\useTheme.ts", "TypeScript", 78, 0, 0, 0, 33, 19, 130
"d:\mydev\Qiankun-Pouch\src\options\index.html", "HTML", 0, 0, 12, 0, 0, 0, 12
"d:\mydev\Qiankun-Pouch\src\options\index.tsx", "TypeScript JSX", 0, 15, 0, 0, 1, 2, 18
"d:\mydev\Qiankun-Pouch\src\options\utils\helpSearch.ts", "TypeScript", 133, 0, 0, 0, 39, 30, 202
"d:\mydev\Qiankun-Pouch\src\options\utils\manifestReader.ts", "TypeScript", 127, 0, 0, 0, 32, 24, 183
"d:\mydev\Qiankun-Pouch\src\options\utils\performance.ts", "TypeScript", 220, 0, 0, 0, 72, 41, 333
"d:\mydev\Qiankun-Pouch\src\pages\test-deleteconfirmmodal.tsx", "TypeScript JSX", 0, 6, 0, 0, 5, 3, 14
"d:\mydev\Qiankun-Pouch\src\popup\PopupApp.tsx", "TypeScript JSX", 0, 617, 0, 0, 54, 62, 733
"d:\mydev\Qiankun-Pouch\src\popup\components\DetailedBookmarkForm.tsx", "TypeScript JSX", 0, 634, 0, 0, 51, 58, 743
"d:\mydev\Qiankun-Pouch\src\popup\components\QuickBookmarkForm.tsx", "TypeScript JSX", 0, 209, 0, 0, 47, 22, 278
"d:\mydev\Qiankun-Pouch\src\popup\components\SmartRecognition.tsx", "TypeScript JSX", 0, 197, 0, 0, 41, 24, 262
"d:\mydev\Qiankun-Pouch\src\popup\components\Toggle.tsx", "TypeScript JSX", 0, 55, 0, 0, 0, 5, 60
"d:\mydev\Qiankun-Pouch\src\popup\components\index.ts", "TypeScript", 2, 0, 0, 0, 0, 0, 2
"d:\mydev\Qiankun-Pouch\src\popup\index.html", "HTML", 0, 0, 12, 0, 0, 0, 12
"d:\mydev\Qiankun-Pouch\src\popup\index.tsx", "TypeScript JSX", 0, 12, 0, 0, 1, 2, 15
"d:\mydev\Qiankun-Pouch\src\services\BookmarkImportExportService.ts", "TypeScript", 1028, 0, 0, 0, 172, 181, 1381
"d:\mydev\Qiankun-Pouch\src\services\CacheManager.ts", "TypeScript", 467, 0, 0, 0, 223, 98, 788
"d:\mydev\Qiankun-Pouch\src\services\ConflictResolverService.ts", "TypeScript", 333, 0, 0, 0, 123, 81, 537
"d:\mydev\Qiankun-Pouch\src\services\ErrorFeedbackService.ts", "TypeScript", 478, 0, 0, 0, 95, 73, 646
"d:\mydev\Qiankun-Pouch\src\services\ErrorRecoveryService.ts", "TypeScript", 341, 0, 0, 0, 102, 60, 503
"d:\mydev\Qiankun-Pouch\src\services\MemoryOptimizedProcessor.ts", "TypeScript", 436, 0, 0, 0, 126, 95, 657
"d:\mydev\Qiankun-Pouch\src\services\SecurityValidator.ts", "TypeScript", 476, 0, 0, 0, 117, 87, 680
"d:\mydev\Qiankun-Pouch\src\services\ViewPreferenceService.ts", "TypeScript", 175, 0, 0, 0, 46, 21, 242
"d:\mydev\Qiankun-Pouch\src\services\WorkerManager.ts", "TypeScript", 286, 0, 0, 0, 121, 57, 464
"d:\mydev\Qiankun-Pouch\src\services\aiCacheService.ts", "TypeScript", 353, 0, 0, 0, 142, 76, 571
"d:\mydev\Qiankun-Pouch\src\services\aiChatService.ts", "TypeScript", 306, 0, 0, 0, 60, 60, 426
"d:\mydev\Qiankun-Pouch\src\services\aiConfigService.ts", "TypeScript", 493, 0, 0, 0, 111, 82, 686
"d:\mydev\Qiankun-Pouch\src\services\aiIntegrationService.ts", "TypeScript", 628, 0, 0, 0, 135, 89, 852
"d:\mydev\Qiankun-Pouch\src\services\aiModelService.ts", "TypeScript", 537, 0, 0, 0, 193, 119, 849
"d:\mydev\Qiankun-Pouch\src\services\aiProviderService.ts", "TypeScript", 3794, 0, 0, 0, 870, 623, 5287
"d:\mydev\Qiankun-Pouch\src\services\aiRecommendationService.ts", "TypeScript", 470, 0, 0, 0, 122, 99, 691
"d:\mydev\Qiankun-Pouch\src\services\aiService.ts", "TypeScript", 1357, 0, 0, 0, 339, 285, 1981
"d:\mydev\Qiankun-Pouch\src\services\bookmarkService.ts", "TypeScript", 393, 0, 0, 0, 167, 84, 644
"d:\mydev\Qiankun-Pouch\src\services\bookmarkStatusService.ts", "TypeScript", 134, 0, 0, 0, 69, 30, 233
"d:\mydev\Qiankun-Pouch\src\services\categoryService.ts", "TypeScript", 439, 0, 0, 0, 183, 92, 714
"d:\mydev\Qiankun-Pouch\src\services\defaultAIModelAPI.ts", "TypeScript", 195, 0, 0, 0, 83, 42, 320
"d:\mydev\Qiankun-Pouch\src\services\defaultAIModelService.ts", "TypeScript", 364, 0, 0, 0, 103, 61, 528
"d:\mydev\Qiankun-Pouch\src\services\localAIServiceAdapter.ts", "TypeScript", 387, 0, 0, 0, 120, 74, 581
"d:\mydev\Qiankun-Pouch\src\services\mcpTestService.ts", "TypeScript", 208, 0, 0, 0, 59, 41, 308
"d:\mydev\Qiankun-Pouch\src\services\pageInfoService.ts", "TypeScript", 277, 0, 0, 0, 100, 62, 439
"d:\mydev\Qiankun-Pouch\src\services\tabStatusManager.ts", "TypeScript", 186, 0, 0, 0, 75, 44, 305
"d:\mydev\Qiankun-Pouch\src\services\tagService.ts", "TypeScript", 446, 0, 0, 0, 171, 95, 712
"d:\mydev\Qiankun-Pouch\src\styles\globals.css", "PostCSS", 0, 0, 0, 439, 38, 89, 566
"d:\mydev\Qiankun-Pouch\src\styles\responsive.css", "PostCSS", 0, 0, 0, 335, 21, 95, 451
"d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.html", "HTML", 0, 0, 20, 0, 0, 1, 21
"d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.tsx", "TypeScript JSX", 0, 196, 0, 0, 4, 15, 215
"d:\mydev\Qiankun-Pouch\src\types\ai.ts", "TypeScript", 191, 0, 0, 0, 70, 28, 289
"d:\mydev\Qiankun-Pouch\src\types\import-export.ts", "TypeScript", 129, 0, 0, 0, 22, 22, 173
"d:\mydev\Qiankun-Pouch\src\types\index.ts", "TypeScript", 337, 0, 0, 0, 33, 42, 412
"d:\mydev\Qiankun-Pouch\src\types\layout.ts", "TypeScript", 91, 0, 0, 0, 38, 15, 144
"d:\mydev\Qiankun-Pouch\src\types\messages.ts", "TypeScript", 330, 0, 0, 0, 34, 59, 423
"d:\mydev\Qiankun-Pouch\src\types\shadcn.ts", "TypeScript", 185, 0, 0, 0, 52, 62, 299
"d:\mydev\Qiankun-Pouch\src\types\validation.ts", "TypeScript", 40, 0, 0, 0, 9, 8, 57
"d:\mydev\Qiankun-Pouch\src\utils\bookmarkSortUtils.ts", "TypeScript", 113, 0, 0, 0, 36, 20, 169
"d:\mydev\Qiankun-Pouch\src\utils\chromeStorage.ts", "TypeScript", 427, 0, 0, 0, 117, 65, 609
"d:\mydev\Qiankun-Pouch\src\utils\chromeTestUtils.ts", "TypeScript", 176, 0, 0, 0, 31, 16, 223
"d:\mydev\Qiankun-Pouch\src\utils\colorUtils.ts", "TypeScript", 290, 0, 0, 0, 134, 69, 493
"d:\mydev\Qiankun-Pouch\src\utils\dataMigration.ts", "TypeScript", 270, 0, 0, 0, 79, 56, 405
"d:\mydev\Qiankun-Pouch\src\utils\debounce.ts", "TypeScript", 80, 0, 0, 0, 37, 17, 134
"d:\mydev\Qiankun-Pouch\src\utils\errorHandler.ts", "TypeScript", 60, 0, 0, 0, 27, 10, 97
"d:\mydev\Qiankun-Pouch\src\utils\errorHandler.tsx", "TypeScript JSX", 0, 254, 0, 0, 89, 50, 393
"d:\mydev\Qiankun-Pouch\src\utils\fallbackStorage.ts", "TypeScript", 263, 0, 0, 0, 98, 67, 428
"d:\mydev\Qiankun-Pouch\src\utils\indexedDB.ts", "TypeScript", 693, 0, 0, 0, 208, 191, 1092
"d:\mydev\Qiankun-Pouch\src\utils\layoutStability.ts", "TypeScript", 213, 0, 0, 0, 45, 53, 311
"d:\mydev\Qiankun-Pouch\src\utils\layoutUtils.ts", "TypeScript", 233, 0, 0, 0, 98, 48, 379
"d:\mydev\Qiankun-Pouch\src\utils\messaging.ts", "TypeScript", 252, 0, 0, 0, 66, 54, 372
"d:\mydev\Qiankun-Pouch\src\utils\modelFactory.ts", "TypeScript", 195, 0, 0, 0, 111, 47, 353
"d:\mydev\Qiankun-Pouch\src\utils\performance.ts", "TypeScript", 258, 0, 0, 0, 69, 65, 392
"d:\mydev\Qiankun-Pouch\src\utils\searchUtils.ts", "TypeScript", 328, 0, 0, 0, 118, 72, 518
"d:\mydev\Qiankun-Pouch\src\utils\serialization.ts", "TypeScript", 237, 0, 0, 0, 97, 34, 368
"d:\mydev\Qiankun-Pouch\src\utils\tagUtils.ts", "TypeScript", 289, 0, 0, 0, 109, 53, 451
"d:\mydev\Qiankun-Pouch\src\utils\textUtils.ts", "TypeScript", 232, 0, 0, 0, 73, 61, 366
"d:\mydev\Qiankun-Pouch\src\utils\validation.ts", "TypeScript", 274, 0, 0, 0, 114, 62, 450
"d:\mydev\Qiankun-Pouch\src\utils\virtualScroll.ts", "TypeScript", 287, 0, 0, 0, 80, 57, 424
"d:\mydev\Qiankun-Pouch\src\workers\DataProcessingWorker.ts", "TypeScript", 410, 0, 0, 0, 152, 104, 666
"Total", "-", 26058, 29110, 103, 846, 9955, 8302, 74374