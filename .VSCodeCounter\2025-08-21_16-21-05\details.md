# Details

Date : 2025-08-21 16:21:05

Directory d:\\mydev\\Qiankun-Pouch\\src

Total : 232 files,  56117 codes, 9955 comments, 8302 blanks, all 74374 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/background/index.ts](/src/background/index.ts) | TypeScript | 92 | 18 | 22 | 132 |
| [src/background/messageHandler.ts](/src/background/messageHandler.ts) | TypeScript | 782 | 189 | 136 | 1,107 |
| [src/components/AIConfigPanel.tsx](/src/components/AIConfigPanel.tsx) | TypeScript JSX | 411 | 19 | 45 | 475 |
| [src/components/AIIntegrationTab.tsx](/src/components/AIIntegrationTab.tsx) | TypeScript JSX | 834 | 57 | 88 | 979 |
| [src/components/AIRecommendations.tsx](/src/components/AIRecommendations.tsx) | TypeScript JSX | 553 | 131 | 92 | 776 |
| [src/components/AITextGenerator.tsx](/src/components/AITextGenerator.tsx) | TypeScript JSX | 270 | 39 | 30 | 339 |
| [src/components/AddBookmarkModal.tsx](/src/components/AddBookmarkModal.tsx) | TypeScript JSX | 551 | 45 | 48 | 644 |
| [src/components/BookmarkCompact.tsx](/src/components/BookmarkCompact.tsx) | TypeScript JSX | 265 | 39 | 25 | 329 |
| [src/components/BookmarkEditModal.tsx](/src/components/BookmarkEditModal.tsx) | TypeScript JSX | 603 | 53 | 58 | 714 |
| [src/components/BookmarkRow.tsx](/src/components/BookmarkRow.tsx) | TypeScript JSX | 229 | 35 | 22 | 286 |
| [src/components/BookmarkSortSelector.tsx](/src/components/BookmarkSortSelector.tsx) | TypeScript JSX | 45 | 18 | 9 | 72 |
| [src/components/BookmarkToolbar.tsx](/src/components/BookmarkToolbar.tsx) | TypeScript JSX | 227 | 31 | 31 | 289 |
| [src/components/BookmarksTab.tsx](/src/components/BookmarksTab.tsx) | TypeScript JSX | 424 | 92 | 58 | 574 |
| [src/components/CategoryCard.tsx](/src/components/CategoryCard.tsx) | TypeScript JSX | 219 | 40 | 28 | 287 |
| [src/components/CategoryForm.tsx](/src/components/CategoryForm.tsx) | TypeScript JSX | 294 | 33 | 33 | 360 |
| [src/components/CategoryList.tsx](/src/components/CategoryList.tsx) | TypeScript JSX | 114 | 20 | 14 | 148 |
| [src/components/CategoryManagementTab.tsx](/src/components/CategoryManagementTab.tsx) | TypeScript JSX | 208 | 31 | 36 | 275 |
| [src/components/CategoryModal.tsx](/src/components/CategoryModal.tsx) | TypeScript JSX | 205 | 32 | 24 | 261 |
| [src/components/ConflictResolutionDialog.tsx](/src/components/ConflictResolutionDialog.tsx) | TypeScript JSX | 283 | 19 | 26 | 328 |
| [src/components/ConflictResolution/BatchActionsPanel.tsx](/src/components/ConflictResolution/BatchActionsPanel.tsx) | TypeScript JSX | 34 | 1 | 4 | 39 |
| [src/components/ConflictResolution/ConflictListPanel.tsx](/src/components/ConflictResolution/ConflictListPanel.tsx) | TypeScript JSX | 74 | 1 | 8 | 83 |
| [src/components/ConflictResolution/ManualEditForm.tsx](/src/components/ConflictResolution/ManualEditForm.tsx) | TypeScript JSX | 72 | 1 | 7 | 80 |
| [src/components/ConflictResolution/utils.ts](/src/components/ConflictResolution/utils.ts) | TypeScript | 36 | 5 | 4 | 45 |
| [src/components/DefaultAIModelsTab.tsx](/src/components/DefaultAIModelsTab.tsx) | TypeScript JSX | 520 | 31 | 50 | 601 |
| [src/components/DeleteConfirmModal.tsx](/src/components/DeleteConfirmModal.tsx) | TypeScript JSX | 221 | 25 | 24 | 270 |
| [src/components/HelpTooltip.tsx](/src/components/HelpTooltip.tsx) | TypeScript JSX | 243 | 27 | 29 | 299 |
| [src/components/HelpTooltip/helpContent.ts](/src/components/HelpTooltip/helpContent.ts) | TypeScript | 73 | 10 | 4 | 87 |
| [src/components/HelpTooltip/useTooltip.ts](/src/components/HelpTooltip/useTooltip.ts) | TypeScript | 93 | 16 | 18 | 127 |
| [src/components/ImportExportTab.tsx](/src/components/ImportExportTab.tsx) | TypeScript JSX | 719 | 43 | 78 | 840 |
| [src/components/LazyLoadWrapper.tsx](/src/components/LazyLoadWrapper.tsx) | TypeScript JSX | 302 | 31 | 53 | 386 |
| [src/components/LoadingIndicator.tsx](/src/components/LoadingIndicator.tsx) | TypeScript JSX | 86 | 19 | 13 | 118 |
| [src/components/MCPSettingsTab.tsx](/src/components/MCPSettingsTab.tsx) | TypeScript JSX | 884 | 48 | 82 | 1,014 |
| [src/components/ManagementPageLayout.tsx](/src/components/ManagementPageLayout.tsx) | TypeScript JSX | 81 | 25 | 11 | 117 |
| [src/components/ModelSelector.tsx](/src/components/ModelSelector.tsx) | TypeScript JSX | 335 | 10 | 21 | 366 |
| [src/components/NotionSyncTab.tsx](/src/components/NotionSyncTab.tsx) | TypeScript JSX | 366 | 18 | 23 | 407 |
| [src/components/ObsidianIntegrationTab.tsx](/src/components/ObsidianIntegrationTab.tsx) | TypeScript JSX | 667 | 23 | 52 | 742 |
| [src/components/OptionsPageErrorBoundary.tsx](/src/components/OptionsPageErrorBoundary.tsx) | TypeScript JSX | 238 | 40 | 36 | 314 |
| [src/components/SmartFolderSelector.tsx](/src/components/SmartFolderSelector.tsx) | TypeScript JSX | 274 | 54 | 34 | 362 |
| [src/components/SmartTagInput.tsx](/src/components/SmartTagInput.tsx) | TypeScript JSX | 296 | 56 | 31 | 383 |
| [src/components/SuperMarketTab.tsx](/src/components/SuperMarketTab.tsx) | TypeScript JSX | 445 | 23 | 29 | 497 |
| [src/components/TagBatchActions.tsx](/src/components/TagBatchActions.tsx) | TypeScript JSX | 328 | 26 | 24 | 378 |
| [src/components/TagCard.tsx](/src/components/TagCard.tsx) | TypeScript JSX | 209 | 39 | 30 | 278 |
| [src/components/TagCloud.tsx](/src/components/TagCloud.tsx) | TypeScript JSX | 149 | 28 | 20 | 197 |
| [src/components/TagColorPicker.tsx](/src/components/TagColorPicker.tsx) | TypeScript JSX | 232 | 48 | 23 | 303 |
| [src/components/TagForm.tsx](/src/components/TagForm.tsx) | TypeScript JSX | 285 | 66 | 40 | 391 |
| [src/components/TagList.tsx](/src/components/TagList.tsx) | TypeScript JSX | 415 | 55 | 46 | 516 |
| [src/components/TagManagementTab.tsx](/src/components/TagManagementTab.tsx) | TypeScript JSX | 323 | 44 | 58 | 425 |
| [src/components/TagModal.tsx](/src/components/TagModal.tsx) | TypeScript JSX | 212 | 34 | 25 | 271 |
| [src/components/TagsTab.tsx](/src/components/TagsTab.tsx) | TypeScript JSX | 133 | 20 | 18 | 171 |
| [src/components/Toast.tsx](/src/components/Toast.tsx) | TypeScript JSX | 119 | 21 | 17 | 157 |
| [src/components/ToastContainer.tsx](/src/components/ToastContainer.tsx) | TypeScript JSX | 38 | 8 | 5 | 51 |
| [src/components/TruncatedTitle.tsx](/src/components/TruncatedTitle.tsx) | TypeScript JSX | 138 | 31 | 27 | 196 |
| [src/components/ViewModeSelector.tsx](/src/components/ViewModeSelector.tsx) | TypeScript JSX | 104 | 15 | 10 | 129 |
| [src/components/VirtualBookmarkList.tsx](/src/components/VirtualBookmarkList.tsx) | TypeScript JSX | 276 | 22 | 24 | 322 |
| [src/components/VirtualScrollList.tsx](/src/components/VirtualScrollList.tsx) | TypeScript JSX | 230 | 36 | 38 | 304 |
| [src/components/examples/AddBookmarkModalDemo.tsx](/src/components/examples/AddBookmarkModalDemo.tsx) | TypeScript JSX | 165 | 17 | 18 | 200 |
| [src/components/examples/AdvancedComponentsDemo.tsx](/src/components/examples/AdvancedComponentsDemo.tsx) | TypeScript JSX | 167 | 9 | 11 | 187 |
| [src/components/examples/BasicComponentsDemo.tsx](/src/components/examples/BasicComponentsDemo.tsx) | TypeScript JSX | 176 | 8 | 8 | 192 |
| [src/components/examples/BookmarkCompactDemo.tsx](/src/components/examples/BookmarkCompactDemo.tsx) | TypeScript JSX | 291 | 10 | 21 | 322 |
| [src/components/examples/BookmarkEditModalDemo.tsx](/src/components/examples/BookmarkEditModalDemo.tsx) | TypeScript JSX | 108 | 10 | 13 | 131 |
| [src/components/examples/BookmarkRowDemo.tsx](/src/components/examples/BookmarkRowDemo.tsx) | TypeScript JSX | 149 | 6 | 11 | 166 |
| [src/components/examples/DeleteConfirmModalDemo.tsx](/src/components/examples/DeleteConfirmModalDemo.tsx) | TypeScript JSX | 178 | 14 | 21 | 213 |
| [src/components/examples/PopupAppDemo.tsx](/src/components/examples/PopupAppDemo.tsx) | TypeScript JSX | 56 | 9 | 8 | 73 |
| [src/components/examples/VirtualBookmarkListDemo.tsx](/src/components/examples/VirtualBookmarkListDemo.tsx) | TypeScript JSX | 202 | 16 | 22 | 240 |
| [src/components/test/AIGeneratorTest.tsx](/src/components/test/AIGeneratorTest.tsx) | TypeScript JSX | 361 | 28 | 30 | 419 |
| [src/components/test/AIModelChatTest.tsx](/src/components/test/AIModelChatTest.tsx) | TypeScript JSX | 659 | 35 | 75 | 769 |
| [src/components/test/BookmarkCompactTest.tsx](/src/components/test/BookmarkCompactTest.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/components/test/BookmarkEditModalTest.tsx](/src/components/test/BookmarkEditModalTest.tsx) | TypeScript JSX | 223 | 12 | 18 | 253 |
| [src/components/test/BookmarkSortTest.tsx](/src/components/test/BookmarkSortTest.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/components/test/BookmarkTagColorTest.tsx](/src/components/test/BookmarkTagColorTest.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/components/test/BookmarkToolbarTest.tsx](/src/components/test/BookmarkToolbarTest.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/components/test/BookmarksTabTestPage.tsx](/src/components/test/BookmarksTabTestPage.tsx) | TypeScript JSX | 233 | 19 | 18 | 270 |
| [src/components/test/CardImportTest.tsx](/src/components/test/CardImportTest.tsx) | TypeScript JSX | 18 | 1 | 3 | 22 |
| [src/components/test/CloudAIServiceTest.tsx](/src/components/test/CloudAIServiceTest.tsx) | TypeScript JSX | 814 | 35 | 85 | 934 |
| [src/components/test/DeleteConfirmModalTest.tsx](/src/components/test/DeleteConfirmModalTest.tsx) | TypeScript JSX | 321 | 20 | 26 | 367 |
| [src/components/test/DeleteConfirmModalTestPage.tsx](/src/components/test/DeleteConfirmModalTestPage.tsx) | TypeScript JSX | 418 | 23 | 30 | 471 |
| [src/components/test/LocalAIServiceTestPage.tsx](/src/components/test/LocalAIServiceTestPage.tsx) | TypeScript JSX | 540 | 27 | 67 | 634 |
| [src/components/test/OtherComponentsTestPage.tsx](/src/components/test/OtherComponentsTestPage.tsx) | TypeScript JSX | 274 | 9 | 21 | 304 |
| [src/components/test/PopupAppTest.tsx](/src/components/test/PopupAppTest.tsx) | TypeScript JSX | 150 | 18 | 18 | 186 |
| [src/components/test/PopupAppTestPage.tsx](/src/components/test/PopupAppTestPage.tsx) | TypeScript JSX | 6 | 5 | 2 | 13 |
| [src/components/test/ShadcnModalTest.tsx](/src/components/test/ShadcnModalTest.tsx) | TypeScript JSX | 173 | 9 | 15 | 197 |
| [src/components/test/ShadcnTest.tsx](/src/components/test/ShadcnTest.tsx) | TypeScript JSX | 26 | 7 | 4 | 37 |
| [src/components/test/TestErrorBoundary.tsx](/src/components/test/TestErrorBoundary.tsx) | TypeScript JSX | 106 | 7 | 15 | 128 |
| [src/components/test/VirtualBookmarkListTest.tsx](/src/components/test/VirtualBookmarkListTest.tsx) | TypeScript JSX | 352 | 26 | 35 | 413 |
| [src/components/test/VirtualBookmarkListTestPage.tsx](/src/components/test/VirtualBookmarkListTestPage.tsx) | TypeScript JSX | 252 | 12 | 12 | 276 |
| [src/components/ui/alert-dialog.tsx](/src/components/ui/alert-dialog.tsx) | TypeScript JSX | 126 | 0 | 14 | 140 |
| [src/components/ui/alert.tsx](/src/components/ui/alert.tsx) | TypeScript JSX | 53 | 0 | 7 | 60 |
| [src/components/ui/badge.tsx](/src/components/ui/badge.tsx) | TypeScript JSX | 31 | 0 | 6 | 37 |
| [src/components/ui/button.tsx](/src/components/ui/button.tsx) | TypeScript JSX | 51 | 0 | 6 | 57 |
| [src/components/ui/card.tsx](/src/components/ui/card.tsx) | TypeScript JSX | 71 | 0 | 9 | 80 |
| [src/components/ui/checkbox.tsx](/src/components/ui/checkbox.tsx) | TypeScript JSX | 25 | 0 | 4 | 29 |
| [src/components/ui/dialog.tsx](/src/components/ui/dialog.tsx) | TypeScript JSX | 108 | 0 | 13 | 121 |
| [src/components/ui/dropdown-menu.tsx](/src/components/ui/dropdown-menu.tsx) | TypeScript JSX | 181 | 0 | 18 | 199 |
| [src/components/ui/form.tsx](/src/components/ui/form.tsx) | TypeScript JSX | 152 | 0 | 25 | 177 |
| [src/components/ui/input.tsx](/src/components/ui/input.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/label.tsx](/src/components/ui/label.tsx) | TypeScript JSX | 20 | 0 | 5 | 25 |
| [src/components/ui/progress.tsx](/src/components/ui/progress.tsx) | TypeScript JSX | 23 | 0 | 4 | 27 |
| [src/components/ui/select.tsx](/src/components/ui/select.tsx) | TypeScript JSX | 145 | 0 | 13 | 158 |
| [src/components/ui/separator.tsx](/src/components/ui/separator.tsx) | TypeScript JSX | 26 | 0 | 4 | 30 |
| [src/components/ui/switch.tsx](/src/components/ui/switch.tsx) | TypeScript JSX | 24 | 0 | 4 | 28 |
| [src/components/ui/tabs.tsx](/src/components/ui/tabs.tsx) | TypeScript JSX | 47 | 0 | 6 | 53 |
| [src/components/ui/textarea.tsx](/src/components/ui/textarea.tsx) | TypeScript JSX | 20 | 0 | 4 | 24 |
| [src/components/ui/tooltip.tsx](/src/components/ui/tooltip.tsx) | TypeScript JSX | 22 | 0 | 7 | 29 |
| [src/constants/index.ts](/src/constants/index.ts) | TypeScript | 1 | 2 | 1 | 4 |
| [src/constants/shadcn.ts](/src/constants/shadcn.ts) | TypeScript | 250 | 19 | 25 | 294 |
| [src/content/index.ts](/src/content/index.ts) | TypeScript | 78 | 12 | 20 | 110 |
| [src/content/style.css](/src/content/style.css) | PostCSS | 72 | 7 | 11 | 90 |
| [src/demo/addBookmarkModal.html](/src/demo/addBookmarkModal.html) | HTML | 20 | 0 | 1 | 21 |
| [src/demo/addBookmarkModalTest.html](/src/demo/addBookmarkModalTest.html) | HTML | 26 | 0 | 1 | 27 |
| [src/demo/addBookmarkModalTest.tsx](/src/demo/addBookmarkModalTest.tsx) | TypeScript JSX | 12 | 2 | 3 | 17 |
| [src/demo/management-layout-demo.html](/src/demo/management-layout-demo.html) | HTML | 13 | 0 | 1 | 14 |
| [src/demo/management-layout-demo.tsx](/src/demo/management-layout-demo.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/examples/BookmarkSortDemo.tsx](/src/examples/BookmarkSortDemo.tsx) | TypeScript JSX | 279 | 11 | 16 | 306 |
| [src/examples/BookmarkTagColorDemo.tsx](/src/examples/BookmarkTagColorDemo.tsx) | TypeScript JSX | 233 | 12 | 13 | 258 |
| [src/examples/BookmarkToolbarDemo.tsx](/src/examples/BookmarkToolbarDemo.tsx) | TypeScript JSX | 242 | 17 | 36 | 295 |
| [src/examples/CategoryCardDemo.tsx](/src/examples/CategoryCardDemo.tsx) | TypeScript JSX | 147 | 2 | 12 | 161 |
| [src/examples/CategoryFormDemo.tsx](/src/examples/CategoryFormDemo.tsx) | TypeScript JSX | 208 | 13 | 27 | 248 |
| [src/examples/CategoryModalDemo.tsx](/src/examples/CategoryModalDemo.tsx) | TypeScript JSX | 203 | 20 | 22 | 245 |
| [src/examples/ManagementLayoutDemo.tsx](/src/examples/ManagementLayoutDemo.tsx) | TypeScript JSX | 168 | 4 | 15 | 187 |
| [src/examples/TagCardDemo.tsx](/src/examples/TagCardDemo.tsx) | TypeScript JSX | 267 | 22 | 17 | 306 |
| [src/examples/TagColorPickerDemo.tsx](/src/examples/TagColorPickerDemo.tsx) | TypeScript JSX | 163 | 12 | 11 | 186 |
| [src/examples/TagFormDemo.tsx](/src/examples/TagFormDemo.tsx) | TypeScript JSX | 238 | 25 | 21 | 284 |
| [src/examples/TagListDemo.tsx](/src/examples/TagListDemo.tsx) | TypeScript JSX | 184 | 18 | 23 | 225 |
| [src/examples/TagManagementTabDemo.tsx](/src/examples/TagManagementTabDemo.tsx) | TypeScript JSX | 14 | 5 | 3 | 22 |
| [src/examples/TagModalDemo.tsx](/src/examples/TagModalDemo.tsx) | TypeScript JSX | 133 | 18 | 18 | 169 |
| [src/examples/ViewModeSelectorExample.tsx](/src/examples/ViewModeSelectorExample.tsx) | TypeScript JSX | 99 | 8 | 11 | 118 |
| [src/hooks/useAdvancedSearch.ts](/src/hooks/useAdvancedSearch.ts) | TypeScript | 249 | 44 | 48 | 341 |
| [src/hooks/useConflictResolution.ts](/src/hooks/useConflictResolution.ts) | TypeScript | 143 | 18 | 23 | 184 |
| [src/hooks/useLoadingState.ts](/src/hooks/useLoadingState.ts) | TypeScript | 63 | 17 | 11 | 91 |
| [src/hooks/useShadcn.ts](/src/hooks/useShadcn.ts) | TypeScript | 308 | 16 | 61 | 385 |
| [src/hooks/useTagColors.ts](/src/hooks/useTagColors.ts) | TypeScript | 48 | 26 | 12 | 86 |
| [src/hooks/useToast.ts](/src/hooks/useToast.ts) | TypeScript | 72 | 21 | 15 | 108 |
| [src/hooks/useViewMode.ts](/src/hooks/useViewMode.ts) | TypeScript | 44 | 12 | 9 | 65 |
| [src/lib/shadcn-config.ts](/src/lib/shadcn-config.ts) | TypeScript | 227 | 39 | 34 | 300 |
| [src/lib/utils.ts](/src/lib/utils.ts) | TypeScript | 5 | 0 | 2 | 7 |
| [src/options/OptionsApp.tsx](/src/options/OptionsApp.tsx) | TypeScript JSX | 61 | 13 | 13 | 87 |
| [src/options/components/AIAssistantTab.tsx](/src/options/components/AIAssistantTab.tsx) | TypeScript JSX | 66 | 2 | 6 | 74 |
| [src/options/components/AboutTab.tsx](/src/options/components/AboutTab.tsx) | TypeScript JSX | 260 | 19 | 25 | 304 |
| [src/options/components/AppHeader.tsx](/src/options/components/AppHeader.tsx) | TypeScript JSX | 33 | 3 | 4 | 40 |
| [src/options/components/ErrorState.tsx](/src/options/components/ErrorState.tsx) | TypeScript JSX | 62 | 1 | 3 | 66 |
| [src/options/components/HelpCenterTab.tsx](/src/options/components/HelpCenterTab.tsx) | TypeScript JSX | 469 | 33 | 38 | 540 |
| [src/options/components/HelpSearchBox.tsx](/src/options/components/HelpSearchBox.tsx) | TypeScript JSX | 217 | 26 | 32 | 275 |
| [src/options/components/LoadingState.tsx](/src/options/components/LoadingState.tsx) | TypeScript JSX | 22 | 1 | 3 | 26 |
| [src/options/components/NavigationSidebar.tsx](/src/options/components/NavigationSidebar.tsx) | TypeScript JSX | 118 | 2 | 5 | 125 |
| [src/options/components/PageErrorBoundary.tsx](/src/options/components/PageErrorBoundary.tsx) | TypeScript JSX | 231 | 36 | 39 | 306 |
| [src/options/components/SettingsTab.tsx](/src/options/components/SettingsTab.tsx) | TypeScript JSX | 84 | 2 | 6 | 92 |
| [src/options/components/SyncTab.tsx](/src/options/components/SyncTab.tsx) | TypeScript JSX | 77 | 2 | 6 | 85 |
| [src/options/components/TabContentRenderer.tsx](/src/options/components/TabContentRenderer.tsx) | TypeScript JSX | 101 | 5 | 8 | 114 |
| [src/options/components/ThemeToggle.tsx](/src/options/components/ThemeToggle.tsx) | TypeScript JSX | 129 | 8 | 11 | 148 |
| [src/options/constants/tabsConfig.ts](/src/options/constants/tabsConfig.ts) | TypeScript | 66 | 10 | 7 | 83 |
| [src/options/data/aboutInfo.ts](/src/options/data/aboutInfo.ts) | TypeScript | 52 | 5 | 3 | 60 |
| [src/options/data/helpContent.ts](/src/options/data/helpContent.ts) | TypeScript | 275 | 10 | 9 | 294 |
| [src/options/hooks/useAppInitialization.ts](/src/options/hooks/useAppInitialization.ts) | TypeScript | 40 | 7 | 8 | 55 |
| [src/options/hooks/useCache.ts](/src/options/hooks/useCache.ts) | TypeScript | 277 | 35 | 64 | 376 |
| [src/options/hooks/useLazyLoad.ts](/src/options/hooks/useLazyLoad.ts) | TypeScript | 158 | 18 | 26 | 202 |
| [src/options/hooks/useResponsive.ts](/src/options/hooks/useResponsive.ts) | TypeScript | 119 | 34 | 23 | 176 |
| [src/options/hooks/useTabNavigation.ts](/src/options/hooks/useTabNavigation.ts) | TypeScript | 77 | 11 | 14 | 102 |
| [src/options/hooks/useTheme.ts](/src/options/hooks/useTheme.ts) | TypeScript | 78 | 33 | 19 | 130 |
| [src/options/index.html](/src/options/index.html) | HTML | 12 | 0 | 0 | 12 |
| [src/options/index.tsx](/src/options/index.tsx) | TypeScript JSX | 15 | 1 | 2 | 18 |
| [src/options/utils/helpSearch.ts](/src/options/utils/helpSearch.ts) | TypeScript | 133 | 39 | 30 | 202 |
| [src/options/utils/manifestReader.ts](/src/options/utils/manifestReader.ts) | TypeScript | 127 | 32 | 24 | 183 |
| [src/options/utils/performance.ts](/src/options/utils/performance.ts) | TypeScript | 220 | 72 | 41 | 333 |
| [src/pages/test-deleteconfirmmodal.tsx](/src/pages/test-deleteconfirmmodal.tsx) | TypeScript JSX | 6 | 5 | 3 | 14 |
| [src/popup/PopupApp.tsx](/src/popup/PopupApp.tsx) | TypeScript JSX | 617 | 54 | 62 | 733 |
| [src/popup/components/DetailedBookmarkForm.tsx](/src/popup/components/DetailedBookmarkForm.tsx) | TypeScript JSX | 634 | 51 | 58 | 743 |
| [src/popup/components/QuickBookmarkForm.tsx](/src/popup/components/QuickBookmarkForm.tsx) | TypeScript JSX | 209 | 47 | 22 | 278 |
| [src/popup/components/SmartRecognition.tsx](/src/popup/components/SmartRecognition.tsx) | TypeScript JSX | 197 | 41 | 24 | 262 |
| [src/popup/components/Toggle.tsx](/src/popup/components/Toggle.tsx) | TypeScript JSX | 55 | 0 | 5 | 60 |
| [src/popup/components/index.ts](/src/popup/components/index.ts) | TypeScript | 2 | 0 | 0 | 2 |
| [src/popup/index.html](/src/popup/index.html) | HTML | 12 | 0 | 0 | 12 |
| [src/popup/index.tsx](/src/popup/index.tsx) | TypeScript JSX | 12 | 1 | 2 | 15 |
| [src/services/BookmarkImportExportService.ts](/src/services/BookmarkImportExportService.ts) | TypeScript | 1,028 | 172 | 181 | 1,381 |
| [src/services/CacheManager.ts](/src/services/CacheManager.ts) | TypeScript | 467 | 223 | 98 | 788 |
| [src/services/ConflictResolverService.ts](/src/services/ConflictResolverService.ts) | TypeScript | 333 | 123 | 81 | 537 |
| [src/services/ErrorFeedbackService.ts](/src/services/ErrorFeedbackService.ts) | TypeScript | 478 | 95 | 73 | 646 |
| [src/services/ErrorRecoveryService.ts](/src/services/ErrorRecoveryService.ts) | TypeScript | 341 | 102 | 60 | 503 |
| [src/services/MemoryOptimizedProcessor.ts](/src/services/MemoryOptimizedProcessor.ts) | TypeScript | 436 | 126 | 95 | 657 |
| [src/services/SecurityValidator.ts](/src/services/SecurityValidator.ts) | TypeScript | 476 | 117 | 87 | 680 |
| [src/services/ViewPreferenceService.ts](/src/services/ViewPreferenceService.ts) | TypeScript | 175 | 46 | 21 | 242 |
| [src/services/WorkerManager.ts](/src/services/WorkerManager.ts) | TypeScript | 286 | 121 | 57 | 464 |
| [src/services/aiCacheService.ts](/src/services/aiCacheService.ts) | TypeScript | 353 | 142 | 76 | 571 |
| [src/services/aiChatService.ts](/src/services/aiChatService.ts) | TypeScript | 306 | 60 | 60 | 426 |
| [src/services/aiConfigService.ts](/src/services/aiConfigService.ts) | TypeScript | 493 | 111 | 82 | 686 |
| [src/services/aiIntegrationService.ts](/src/services/aiIntegrationService.ts) | TypeScript | 628 | 135 | 89 | 852 |
| [src/services/aiModelService.ts](/src/services/aiModelService.ts) | TypeScript | 537 | 193 | 119 | 849 |
| [src/services/aiProviderService.ts](/src/services/aiProviderService.ts) | TypeScript | 3,794 | 870 | 623 | 5,287 |
| [src/services/aiRecommendationService.ts](/src/services/aiRecommendationService.ts) | TypeScript | 470 | 122 | 99 | 691 |
| [src/services/aiService.ts](/src/services/aiService.ts) | TypeScript | 1,357 | 339 | 285 | 1,981 |
| [src/services/bookmarkService.ts](/src/services/bookmarkService.ts) | TypeScript | 393 | 167 | 84 | 644 |
| [src/services/bookmarkStatusService.ts](/src/services/bookmarkStatusService.ts) | TypeScript | 134 | 69 | 30 | 233 |
| [src/services/categoryService.ts](/src/services/categoryService.ts) | TypeScript | 439 | 183 | 92 | 714 |
| [src/services/defaultAIModelAPI.ts](/src/services/defaultAIModelAPI.ts) | TypeScript | 195 | 83 | 42 | 320 |
| [src/services/defaultAIModelService.ts](/src/services/defaultAIModelService.ts) | TypeScript | 364 | 103 | 61 | 528 |
| [src/services/localAIServiceAdapter.ts](/src/services/localAIServiceAdapter.ts) | TypeScript | 387 | 120 | 74 | 581 |
| [src/services/mcpTestService.ts](/src/services/mcpTestService.ts) | TypeScript | 208 | 59 | 41 | 308 |
| [src/services/pageInfoService.ts](/src/services/pageInfoService.ts) | TypeScript | 277 | 100 | 62 | 439 |
| [src/services/tabStatusManager.ts](/src/services/tabStatusManager.ts) | TypeScript | 186 | 75 | 44 | 305 |
| [src/services/tagService.ts](/src/services/tagService.ts) | TypeScript | 446 | 171 | 95 | 712 |
| [src/styles/globals.css](/src/styles/globals.css) | PostCSS | 439 | 38 | 89 | 566 |
| [src/styles/responsive.css](/src/styles/responsive.css) | PostCSS | 335 | 21 | 95 | 451 |
| [src/test-pages/management-ui-test.html](/src/test-pages/management-ui-test.html) | HTML | 20 | 0 | 1 | 21 |
| [src/test-pages/management-ui-test.tsx](/src/test-pages/management-ui-test.tsx) | TypeScript JSX | 196 | 4 | 15 | 215 |
| [src/types/ai.ts](/src/types/ai.ts) | TypeScript | 191 | 70 | 28 | 289 |
| [src/types/import-export.ts](/src/types/import-export.ts) | TypeScript | 129 | 22 | 22 | 173 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 337 | 33 | 42 | 412 |
| [src/types/layout.ts](/src/types/layout.ts) | TypeScript | 91 | 38 | 15 | 144 |
| [src/types/messages.ts](/src/types/messages.ts) | TypeScript | 330 | 34 | 59 | 423 |
| [src/types/shadcn.ts](/src/types/shadcn.ts) | TypeScript | 185 | 52 | 62 | 299 |
| [src/types/validation.ts](/src/types/validation.ts) | TypeScript | 40 | 9 | 8 | 57 |
| [src/utils/bookmarkSortUtils.ts](/src/utils/bookmarkSortUtils.ts) | TypeScript | 113 | 36 | 20 | 169 |
| [src/utils/chromeStorage.ts](/src/utils/chromeStorage.ts) | TypeScript | 427 | 117 | 65 | 609 |
| [src/utils/chromeTestUtils.ts](/src/utils/chromeTestUtils.ts) | TypeScript | 176 | 31 | 16 | 223 |
| [src/utils/colorUtils.ts](/src/utils/colorUtils.ts) | TypeScript | 290 | 134 | 69 | 493 |
| [src/utils/dataMigration.ts](/src/utils/dataMigration.ts) | TypeScript | 270 | 79 | 56 | 405 |
| [src/utils/debounce.ts](/src/utils/debounce.ts) | TypeScript | 80 | 37 | 17 | 134 |
| [src/utils/errorHandler.ts](/src/utils/errorHandler.ts) | TypeScript | 60 | 27 | 10 | 97 |
| [src/utils/errorHandler.tsx](/src/utils/errorHandler.tsx) | TypeScript JSX | 254 | 89 | 50 | 393 |
| [src/utils/fallbackStorage.ts](/src/utils/fallbackStorage.ts) | TypeScript | 263 | 98 | 67 | 428 |
| [src/utils/indexedDB.ts](/src/utils/indexedDB.ts) | TypeScript | 693 | 208 | 191 | 1,092 |
| [src/utils/layoutStability.ts](/src/utils/layoutStability.ts) | TypeScript | 213 | 45 | 53 | 311 |
| [src/utils/layoutUtils.ts](/src/utils/layoutUtils.ts) | TypeScript | 233 | 98 | 48 | 379 |
| [src/utils/messaging.ts](/src/utils/messaging.ts) | TypeScript | 252 | 66 | 54 | 372 |
| [src/utils/modelFactory.ts](/src/utils/modelFactory.ts) | TypeScript | 195 | 111 | 47 | 353 |
| [src/utils/performance.ts](/src/utils/performance.ts) | TypeScript | 258 | 69 | 65 | 392 |
| [src/utils/searchUtils.ts](/src/utils/searchUtils.ts) | TypeScript | 328 | 118 | 72 | 518 |
| [src/utils/serialization.ts](/src/utils/serialization.ts) | TypeScript | 237 | 97 | 34 | 368 |
| [src/utils/tagUtils.ts](/src/utils/tagUtils.ts) | TypeScript | 289 | 109 | 53 | 451 |
| [src/utils/textUtils.ts](/src/utils/textUtils.ts) | TypeScript | 232 | 73 | 61 | 366 |
| [src/utils/validation.ts](/src/utils/validation.ts) | TypeScript | 274 | 114 | 62 | 450 |
| [src/utils/virtualScroll.ts](/src/utils/virtualScroll.ts) | TypeScript | 287 | 80 | 57 | 424 |
| [src/workers/DataProcessingWorker.ts](/src/workers/DataProcessingWorker.ts) | TypeScript | 410 | 152 | 104 | 666 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)