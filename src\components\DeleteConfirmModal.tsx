// 删除确认模态组件 - 使用shadcn AlertDialog重构

import React, { useState } from 'react'
import { Trash2, AlertTriangle, Bookmark, FileText, Link } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import TruncatedTitle from './TruncatedTitle'
import { useTagColors } from '../hooks/useTagColors'

interface DeleteConfirmModalProps {
  /** 是否显示模态窗口 */
  isOpen: boolean
  /** 要删除的收藏项 */
  bookmark: {
    id: string
    title: string
    url?: string
    content?: string
    type: 'url' | 'text' | 'image'
    category?: string
    tags?: string[]
    createdAt: Date | string
  } | null
  /** 删除确认回调函数 */
  onConfirm: (bookmarkId: string) => Promise<void>
  /** 取消回调函数 */
  onCancel: () => void
  /** 是否正在删除 */
  loading?: boolean
  /** 是否支持撤销功能 */
  enableUndo?: boolean
}

/**
 * 删除确认模态组件 - 使用shadcn AlertDialog重构
 * 提供安全的删除确认机制，显示要删除的收藏信息
 */
const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = React.memo(({
  isOpen,
  bookmark,
  onConfirm,
  onCancel,
  loading = false,
  enableUndo = false
}) => {
  const [isDeleting, setIsDeleting] = useState(false)
  const { getTagColor } = useTagColors()

  // 处理删除确认
  const handleConfirm = async () => {
    if (!bookmark) return

    try {
      setIsDeleting(true)
      await onConfirm(bookmark.id)
    } catch (error) {
      console.error('删除收藏失败:', error)
      // 错误处理由父组件负责
    } finally {
      setIsDeleting(false)
    }
  }

  // 获取收藏类型图标
  const getBookmarkIcon = () => {
    if (!bookmark) return <Bookmark className="w-5 h-5" />
    
    switch (bookmark.type) {
      case 'url':
        return <Link className="w-5 h-5" />
      case 'text':
        return <FileText className="w-5 h-5" />
      case 'image':
        return <Bookmark className="w-5 h-5" />
      default:
        return <Bookmark className="w-5 h-5" />
    }
  }

  // 获取收藏类型文本
  const getBookmarkTypeText = () => {
    if (!bookmark) return '收藏'
    
    switch (bookmark.type) {
      case 'url':
        return '网页收藏'
      case 'text':
        return '文本摘录'
      case 'image':
        return '图片收藏'
      default:
        return '收藏'
    }
  }

  // 格式化创建时间
  const formatDate = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isProcessing = loading || isDeleting

  return (
    <AlertDialog open={isOpen && !!bookmark} onOpenChange={(open) => !open && !isProcessing && onCancel()}>
      <AlertDialogContent className="max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <AlertDialogHeader>
          <div className="flex items-center space-x-3 mb-2">
            <div className="flex-shrink-0 w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-destructive" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">
                确认删除
              </AlertDialogTitle>
              <AlertDialogDescription className="text-left">
                此操作无法撤销
              </AlertDialogDescription>
            </div>
          </div>
        </AlertDialogHeader>

        {bookmark && (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              您确定要删除以下{getBookmarkTypeText()}吗？
            </p>
            
            {/* 收藏信息预览 */}
            <div className="bg-muted/50 rounded-lg p-4 border">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 text-muted-foreground mt-1">
                  {getBookmarkIcon()}
                </div>
                
                <div className="flex-1 min-w-0 overflow-hidden">
                  {/* 标题 */}
                  <div className="font-medium text-foreground mb-1 overflow-hidden">
                    <TruncatedTitle
                      title={bookmark.title || '无标题'}
                      maxLength={40}
                      className="font-medium text-foreground"
                    />
                  </div>

                  {/* URL（如果有） */}
                  {bookmark.url && (
                    <div className="text-sm text-primary mb-2 overflow-hidden">
                      <TruncatedTitle
                        title={bookmark.url}
                        maxLength={45}
                        className="text-sm text-primary break-all"
                      />
                    </div>
                  )}

                  {/* 文本内容预览（如果是文本类型） */}
                  {bookmark.type === 'text' && bookmark.content && (
                    <div className="text-sm text-muted-foreground mb-2 bg-background p-2 rounded border overflow-hidden">
                      <TruncatedTitle
                        title={bookmark.content}
                        maxLength={80}
                        maxLines={3}
                        className="text-sm text-muted-foreground"
                      />
                    </div>
                  )}
                  
                  {/* 元信息 */}
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    {bookmark.category && (
                      <span>分类: {bookmark.category}</span>
                    )}
                    <span>创建: {formatDate(bookmark.createdAt)}</span>
                  </div>
                  
                  {/* 标签 */}
                  {bookmark.tags && bookmark.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {bookmark.tags.slice(0, 3).map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs border"
                          style={{ 
                            borderColor: getTagColor(tag),
                            backgroundColor: `${getTagColor(tag)}15`,
                            color: getTagColor(tag)
                          }}
                        >
                          {tag}
                        </Badge>
                      ))}
                      {bookmark.tags.length > 3 && (
                        <span className="text-xs text-muted-foreground">
                          +{bookmark.tags.length - 3} 更多
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 警告信息 */}
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
                <div className="text-sm text-destructive">
                  <p className="font-medium mb-1">删除后将无法恢复</p>
                  <p>
                    删除操作会永久移除此收藏及其所有相关信息。
                    {enableUndo && ' 您可以在删除后的短时间内撤销此操作。'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isProcessing}>
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isProcessing}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                删除中...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                确认删除
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

// 设置显示名称便于调试
DeleteConfirmModal.displayName = 'DeleteConfirmModal'

export default DeleteConfirmModal

// 导出类型定义
export type { DeleteConfirmModalProps }