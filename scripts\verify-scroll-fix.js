#!/usr/bin/env node

/**
 * 滚动修复验证脚本
 * 用于验证收藏管理页面滚动体验修复的效果
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 颜色输出工具
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(path.resolve(filePath))
}

// 检查文件内容是否包含特定字符串
function checkFileContains(filePath, searchString) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    return content.includes(searchString)
  } catch (error) {
    return false
  }
}

// 验证虚拟滚动修复
function verifyVirtualScrollFix() {
  logInfo('检查虚拟滚动修复...')
  
  const virtualScrollPath = 'src/utils/virtualScroll.ts'
  
  if (!checkFileExists(virtualScrollPath)) {
    logError('虚拟滚动文件不存在')
    return false
  }

  // 检查节流处理
  if (!checkFileContains(virtualScrollPath, 'throttle(')) {
    logError('虚拟滚动缺少节流处理')
    return false
  }

  // 检查配置优化
  if (!checkFileContains(virtualScrollPath, 'config.scrollThrottle')) {
    logError('虚拟滚动配置未优化')
    return false
  }

  logSuccess('虚拟滚动修复验证通过')
  return true
}

// 验证CSS样式修复
function verifyCSSFix() {
  logInfo('检查CSS样式修复...')
  
  const cssPath = 'src/styles/globals.css'
  
  if (!checkFileExists(cssPath)) {
    logError('CSS文件不存在')
    return false
  }

  const requiredStyles = [
    'virtual-scroll-container',
    'overscroll-behavior: contain',
    'overflow-anchor: none',
    '-webkit-overflow-scrolling: touch',
    'touch-action: pan-y'
  ]

  let allStylesPresent = true
  
  for (const style of requiredStyles) {
    if (!checkFileContains(cssPath, style)) {
      logError(`CSS样式缺少: ${style}`)
      allStylesPresent = false
    }
  }

  if (allStylesPresent) {
    logSuccess('CSS样式修复验证通过')
    return true
  }
  
  return false
}

// 验证组件修复
function verifyComponentFix() {
  logInfo('检查组件修复...')
  
  const componentPath = 'src/components/VirtualBookmarkList.tsx'
  
  if (!checkFileExists(componentPath)) {
    logError('VirtualBookmarkList组件不存在')
    return false
  }

  // 检查CSS类使用
  if (!checkFileContains(componentPath, 'virtual-scroll-container')) {
    logError('组件未使用优化的CSS类')
    return false
  }

  // 检查配置优化
  if (!checkFileContains(componentPath, 'overscan: 3')) {
    logError('组件配置未优化')
    return false
  }

  logSuccess('组件修复验证通过')
  return true
}

// 验证测试文件
function verifyTestFiles() {
  logInfo('检查测试文件...')
  
  const testPath = 'tests/scroll-experience-test.tsx'
  
  if (!checkFileExists(testPath)) {
    logWarning('滚动体验测试文件不存在')
    return false
  }

  if (!checkFileContains(testPath, 'ScrollPerformanceTest')) {
    logError('测试文件缺少性能测试组件')
    return false
  }

  logSuccess('测试文件验证通过')
  return true
}

// 生成修复报告
function generateReport(results) {
  logInfo('生成修复报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    fixes: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => r.passed === false).length
    }
  }

  const reportPath = 'docs/scroll-fix-verification-report.json'
  
  try {
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    logSuccess(`修复报告已生成: ${reportPath}`)
  } catch (error) {
    logError(`生成报告失败: ${error.message}`)
  }

  return report
}

// 主验证函数
function main() {
  log('🔍 开始验证滚动修复效果...', 'bold')
  console.log()

  const results = [
    { name: '虚拟滚动修复', passed: verifyVirtualScrollFix() },
    { name: 'CSS样式修复', passed: verifyCSSFix() },
    { name: '组件修复', passed: verifyComponentFix() },
    { name: '测试文件', passed: verifyTestFiles() }
  ]

  console.log()
  log('📊 验证结果汇总:', 'bold')
  
  results.forEach(result => {
    if (result.passed) {
      logSuccess(`${result.name}: 通过`)
    } else {
      logError(`${result.name}: 失败`)
    }
  })

  const report = generateReport(results)
  
  console.log()
  if (report.summary.failed === 0) {
    log('🎉 所有修复验证通过！滚动体验问题已解决。', 'green')
  } else {
    log(`⚠️  ${report.summary.failed}/${report.summary.total} 项验证失败，需要进一步修复。`, 'yellow')
  }

  console.log()
  log('📝 修复说明:', 'bold')
  log('1. 虚拟滚动添加了节流处理，减少滚动事件频率')
  log('2. CSS样式优化了滚动行为，防止回弹和跳跃')
  log('3. 组件配置优化了性能参数，提高渲染效率')
  log('4. 添加了测试用例验证修复效果')

  console.log()
  log('🚀 下一步建议:', 'bold')
  log('1. 运行测试: npm run test tests/scroll-experience-test.tsx')
  log('2. 启动开发服务器测试实际效果: npm run dev')
  log('3. 在不同设备和浏览器上验证滚动体验')
  log('4. 监控生产环境的滚动性能指标')

  process.exit(report.summary.failed === 0 ? 0 : 1)
}

// 运行验证
main()

export {
  verifyVirtualScrollFix,
  verifyCSSFix,
  verifyComponentFix,
  verifyTestFiles,
  generateReport
}
