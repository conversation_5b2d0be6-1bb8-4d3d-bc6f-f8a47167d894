<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>页面滚动修复测试</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
    }

    .test-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #e1e5e9;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .test-title {
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 10px;
      color: #2d3748;
    }

    .test-description {
      color: #4a5568;
      margin-bottom: 20px;
    }

    .test-button {
      background: #3182ce;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .test-button:hover {
      background: #2c5aa0;
    }

    .test-button.danger {
      background: #e53e3e;
    }

    .test-button.danger:hover {
      background: #c53030;
    }

    .status-indicator {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.875em;
      font-weight: bold;
      margin-left: 10px;
    }

    .status-pass {
      background: #c6f6d5;
      color: #22543d;
    }

    .status-fail {
      background: #fed7d7;
      color: #742a2a;
    }

    .status-warning {
      background: #fefcbf;
      color: #744210;
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      padding: 30px;
      border-radius: 8px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
    }

    .modal-header {
      font-size: 1.25em;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .modal-body {
      margin-bottom: 20px;
    }

    .modal-footer {
      text-align: right;
    }

    .content-area {
      height: 200px;
      overflow-y: auto;
      border: 1px solid #e1e5e9;
      padding: 15px;
      margin: 15px 0;
      background: white;
    }

    .long-content {
      height: 2000px;
      background: linear-gradient(to bottom, #f7fafc, #edf2f7);
      padding: 20px;
      margin: 20px 0;
    }

    .scroll-indicator {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #2d3748;
      color: white;
      padding: 10px;
      border-radius: 6px;
      font-family: monospace;
      z-index: 999;
    }

    .test-log {
      background: #1a202c;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: monospace;
      font-size: 0.875em;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 15px;
    }

    .log-entry {
      margin-bottom: 5px;
    }

    .log-timestamp {
      color: #a0aec0;
    }

    .log-level-info {
      color: #63b3ed;
    }

    .log-level-warn {
      color: #f6e05e;
    }

    .log-level-error {
      color: #fc8181;
    }
  </style>
</head>
<body>
  <div class="scroll-indicator" id="scrollIndicator">
    滚动位置: <span id="scrollPosition">0</span>px
  </div>

  <div class="container">
    <h1>页面滚动修复测试</h1>
    <p>此页面用于测试后台管理界面页面滚动问题的修复效果。请按照以下步骤进行测试：</p>

    <!-- 测试1：基础滚动功能 -->
    <div class="test-section">
      <div class="test-title">测试1：基础页面滚动功能</div>
      <div class="test-description">
        验证页面基础滚动功能是否正常工作。页面应该能够正常向下滚动到底部。
      </div>
      <button class="test-button" onclick="testBasicScroll()">测试基础滚动</button>
      <button class="test-button" onclick="scrollToTop()">回到顶部</button>
      <span id="basicScrollStatus" class="status-indicator status-warning">待测试</span>
    </div>

    <!-- 测试2：模态框滚动锁定 -->
    <div class="test-section">
      <div class="test-title">测试2：模态框滚动锁定</div>
      <div class="test-description">
        测试模态框打开时是否正确锁定背景滚动，关闭时是否正确恢复滚动。
      </div>
      <button class="test-button" onclick="openTestModal()">打开测试模态框</button>
      <button class="test-button" onclick="openMultipleModals()">测试多个模态框</button>
      <span id="modalScrollStatus" class="status-indicator status-warning">待测试</span>
    </div>

    <!-- 测试3：快速切换测试 -->
    <div class="test-section">
      <div class="test-title">测试3：快速模态框切换</div>
      <div class="test-description">
        测试快速打开和关闭模态框时，滚动状态是否会出现残留问题。
      </div>
      <button class="test-button" onclick="rapidModalTest()">快速切换测试</button>
      <button class="test-button danger" onclick="forceUnlockScroll()">强制解锁滚动</button>
      <span id="rapidTestStatus" class="status-indicator status-warning">待测试</span>
    </div>

    <!-- 测试4：异常情况恢复 -->
    <div class="test-section">
      <div class="test-title">测试4：异常情况恢复</div>
      <div class="test-description">
        测试在异常情况下（如组件异常卸载）滚动状态是否能够正确恢复。
      </div>
      <button class="test-button" onclick="simulateError()">模拟异常情况</button>
      <button class="test-button" onclick="checkScrollStatus()">检查滚动状态</button>
      <span id="errorRecoveryStatus" class="status-indicator status-warning">待测试</span>
    </div>

    <!-- 滚动管理器状态 -->
    <div class="test-section">
      <div class="test-title">滚动管理器状态</div>
      <div class="test-description">
        实时显示滚动管理器的状态信息，用于调试和监控。
      </div>
      <button class="test-button" onclick="refreshStatus()">刷新状态</button>
      <button class="test-button" onclick="enableDebugMode()">启用调试模式</button>
      <div id="scrollManagerStatus" class="content-area">
        <div>加载中...</div>
      </div>
    </div>

    <!-- 测试日志 -->
    <div class="test-section">
      <div class="test-title">测试日志</div>
      <div class="test-description">
        显示测试过程中的详细日志信息。
      </div>
      <button class="test-button" onclick="clearLog()">清空日志</button>
      <div id="testLog" class="test-log">
        <div class="log-entry">
          <span class="log-timestamp">[00:00:00]</span>
          <span class="log-level-info">[INFO]</span>
          页面滚动测试工具已加载
        </div>
      </div>
    </div>

    <!-- 长内容区域用于测试滚动 -->
    <div class="long-content">
      <h2>长内容区域</h2>
      <p>这是一个很长的内容区域，用于测试页面滚动功能。</p>
      <p>请向下滚动查看更多内容...</p>
      
      <div style="margin: 50px 0;">
        <h3>滚动测试内容</h3>
        <p>如果您能看到这段文字，说明页面滚动功能正常工作。</p>
      </div>

      <div style="margin: 100px 0;">
        <h3>中间位置标记</h3>
        <p>这里是页面的中间位置。</p>
      </div>

      <div style="margin: 200px 0;">
        <h3>更多测试内容</h3>
        <p>继续向下滚动...</p>
      </div>

      <div style="margin: 300px 0;">
        <h3>接近底部</h3>
        <p>您已经接近页面底部了。</p>
      </div>

      <div style="margin: 100px 0; text-align: center; padding: 50px; background: #e2e8f0; border-radius: 8px;">
        <h2>页面底部</h2>
        <p>恭喜！您已经成功滚动到页面底部。</p>
        <button class="test-button" onclick="scrollToTop()">返回顶部</button>
      </div>
    </div>
  </div>

  <!-- 测试模态框 -->
  <div id="testModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">测试模态框</div>
      <div class="modal-body">
        <p>这是一个测试模态框。当模态框打开时，背景应该无法滚动。</p>
        <p>请尝试滚动页面，确认背景滚动已被锁定。</p>
        <div class="content-area">
          <p>模态框内的内容区域</p>
          <p>这个区域有自己的滚动条</p>
          <p>模态框内的滚动应该正常工作</p>
          <p>而背景页面的滚动应该被锁定</p>
          <p>更多内容...</p>
          <p>更多内容...</p>
          <p>更多内容...</p>
          <p>更多内容...</p>
          <p>更多内容...</p>
          <p>更多内容...</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="test-button" onclick="closeTestModal()">关闭模态框</button>
      </div>
    </div>
  </div>

  <script>
    // 模拟滚动管理器（简化版本，用于测试）
    class TestScrollManager {
      constructor() {
        this.locks = new Map()
        this.isLocked = false
        this.originalOverflow = document.body.style.overflow || 'unset'
        this.debugMode = false
      }

      enableDebug() {
        this.debugMode = true
        this.log('调试模式已启用')
      }

      log(message, data = '') {
        if (this.debugMode) {
          console.log(`[ScrollManager] ${message}`, data)
        }
        addLogEntry('INFO', message)
      }

      lock(id, reason = 'test', component = 'TestComponent') {
        const record = {
          id,
          reason,
          timestamp: Date.now(),
          component
        }

        this.locks.set(id, record)
        this.log(`锁定滚动: ${id}`, record)

        if (!this.isLocked) {
          this.originalOverflow = document.body.style.overflow || 'unset'
          document.body.style.overflow = 'hidden'
          this.isLocked = true
          this.log('应用滚动锁定', { originalOverflow: this.originalOverflow })
        }
      }

      unlock(id) {
        const record = this.locks.get(id)
        if (record) {
          this.locks.delete(id)
          this.log(`释放滚动锁定: ${id}`, record)
        }

        if (this.locks.size === 0 && this.isLocked) {
          this.restoreScroll()
        }
      }

      forceUnlockAll() {
        this.log('强制释放所有滚动锁定', { locksCount: this.locks.size })
        this.locks.clear()
        this.restoreScroll()
      }

      restoreScroll() {
        if (this.isLocked) {
          document.body.style.overflow = this.originalOverflow
          this.isLocked = false
          this.log('恢复滚动状态', { overflow: this.originalOverflow })
        }
      }

      getStatus() {
        return {
          isLocked: this.isLocked,
          locksCount: this.locks.size,
          locks: Array.from(this.locks.values()),
          originalOverflow: this.originalOverflow
        }
      }
    }

    // 创建测试滚动管理器实例
    const testScrollManager = new TestScrollManager()

    // 日志功能
    function addLogEntry(level, message) {
      const logContainer = document.getElementById('testLog')
      const timestamp = new Date().toLocaleTimeString()
      const entry = document.createElement('div')
      entry.className = 'log-entry'
      entry.innerHTML = `
        <span class="log-timestamp">[${timestamp}]</span>
        <span class="log-level-${level.toLowerCase()}">[${level}]</span>
        ${message}
      `
      logContainer.appendChild(entry)
      logContainer.scrollTop = logContainer.scrollHeight
    }

    function clearLog() {
      document.getElementById('testLog').innerHTML = ''
      addLogEntry('INFO', '日志已清空')
    }

    // 滚动位置指示器
    function updateScrollIndicator() {
      document.getElementById('scrollPosition').textContent = Math.round(window.pageYOffset)
    }

    window.addEventListener('scroll', updateScrollIndicator)

    // 测试函数
    function testBasicScroll() {
      addLogEntry('INFO', '开始基础滚动测试')
      window.scrollTo({ top: 500, behavior: 'smooth' })
      setTimeout(() => {
        if (window.pageYOffset > 400) {
          document.getElementById('basicScrollStatus').className = 'status-indicator status-pass'
          document.getElementById('basicScrollStatus').textContent = '通过'
          addLogEntry('INFO', '基础滚动测试通过')
        } else {
          document.getElementById('basicScrollStatus').className = 'status-indicator status-fail'
          document.getElementById('basicScrollStatus').textContent = '失败'
          addLogEntry('ERROR', '基础滚动测试失败')
        }
      }, 1000)
    }

    function scrollToTop() {
      window.scrollTo({ top: 0, behavior: 'smooth' })
      addLogEntry('INFO', '滚动到页面顶部')
    }

    function openTestModal() {
      testScrollManager.lock('test-modal', 'modal', 'TestModal')
      document.getElementById('testModal').style.display = 'flex'
      addLogEntry('INFO', '打开测试模态框')
    }

    function closeTestModal() {
      testScrollManager.unlock('test-modal')
      document.getElementById('testModal').style.display = 'none'
      addLogEntry('INFO', '关闭测试模态框')
      
      // 检查滚动状态
      setTimeout(() => {
        const canScroll = document.body.style.overflow !== 'hidden'
        if (canScroll) {
          document.getElementById('modalScrollStatus').className = 'status-indicator status-pass'
          document.getElementById('modalScrollStatus').textContent = '通过'
          addLogEntry('INFO', '模态框滚动锁定测试通过')
        } else {
          document.getElementById('modalScrollStatus').className = 'status-indicator status-fail'
          document.getElementById('modalScrollStatus').textContent = '失败'
          addLogEntry('ERROR', '模态框滚动锁定测试失败 - 滚动未恢复')
        }
      }, 100)
    }

    function openMultipleModals() {
      testScrollManager.lock('modal-1', 'modal', 'Modal1')
      testScrollManager.lock('modal-2', 'modal', 'Modal2')
      addLogEntry('INFO', '模拟多个模态框同时打开')
      
      setTimeout(() => {
        testScrollManager.unlock('modal-1')
        addLogEntry('INFO', '关闭第一个模态框')
      }, 1000)
      
      setTimeout(() => {
        testScrollManager.unlock('modal-2')
        addLogEntry('INFO', '关闭第二个模态框')
      }, 2000)
    }

    function rapidModalTest() {
      addLogEntry('INFO', '开始快速切换测试')
      let testCount = 0
      const maxTests = 10
      
      const rapidTest = () => {
        if (testCount < maxTests) {
          const modalId = `rapid-test-${testCount}`
          testScrollManager.lock(modalId, 'modal', 'RapidTestModal')
          
          setTimeout(() => {
            testScrollManager.unlock(modalId)
            testCount++
            rapidTest()
          }, 100)
        } else {
          // 检查最终状态
          setTimeout(() => {
            const status = testScrollManager.getStatus()
            if (status.locksCount === 0 && !status.isLocked) {
              document.getElementById('rapidTestStatus').className = 'status-indicator status-pass'
              document.getElementById('rapidTestStatus').textContent = '通过'
              addLogEntry('INFO', '快速切换测试通过')
            } else {
              document.getElementById('rapidTestStatus').className = 'status-indicator status-fail'
              document.getElementById('rapidTestStatus').textContent = '失败'
              addLogEntry('ERROR', `快速切换测试失败 - 剩余锁定: ${status.locksCount}`)
            }
          }, 500)
        }
      }
      
      rapidTest()
    }

    function forceUnlockScroll() {
      testScrollManager.forceUnlockAll()
      addLogEntry('WARN', '强制解锁所有滚动锁定')
    }

    function simulateError() {
      // 模拟异常情况：创建锁定但不正常释放
      testScrollManager.lock('error-test', 'modal', 'ErrorComponent')
      addLogEntry('WARN', '模拟异常情况 - 创建未释放的锁定')
      
      // 模拟组件异常卸载（5秒后自动清理）
      setTimeout(() => {
        testScrollManager.forceUnlockAll()
        document.getElementById('errorRecoveryStatus').className = 'status-indicator status-pass'
        document.getElementById('errorRecoveryStatus').textContent = '已恢复'
        addLogEntry('INFO', '异常情况恢复测试完成')
      }, 5000)
    }

    function checkScrollStatus() {
      const status = testScrollManager.getStatus()
      const canScroll = document.body.style.overflow !== 'hidden'
      
      addLogEntry('INFO', `滚动状态检查 - 可滚动: ${canScroll}, 锁定数: ${status.locksCount}`)
      
      if (canScroll && status.locksCount === 0) {
        addLogEntry('INFO', '滚动状态正常')
      } else {
        addLogEntry('WARN', '滚动状态异常')
      }
    }

    function refreshStatus() {
      const status = testScrollManager.getStatus()
      const statusContainer = document.getElementById('scrollManagerStatus')
      
      statusContainer.innerHTML = `
        <div><strong>滚动管理器状态:</strong></div>
        <div>是否锁定: ${status.isLocked ? '是' : '否'}</div>
        <div>锁定数量: ${status.locksCount}</div>
        <div>原始overflow: ${status.originalOverflow}</div>
        <div>当前overflow: ${document.body.style.overflow || 'unset'}</div>
        <div><strong>活跃锁定:</strong></div>
        ${status.locks.map(lock => `
          <div style="margin-left: 20px;">
            ID: ${lock.id}, 原因: ${lock.reason}, 组件: ${lock.component}
          </div>
        `).join('')}
      `
    }

    function enableDebugMode() {
      testScrollManager.enableDebug()
      addLogEntry('INFO', '调试模式已启用')
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      addLogEntry('INFO', '页面滚动测试工具初始化完成')
      refreshStatus()
      
      // 定期刷新状态
      setInterval(refreshStatus, 2000)
    })

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeTestModal()
      }
    })
  </script>
</body>
</html>
