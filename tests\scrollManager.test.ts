/**
 * 滚动管理器测试
 * 测试滚动状态管理工具的各种功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import {
  scrollManager,
  useModalScrollLock,
  useDialogScrollLock,
  ScrollLockReason
} from '../src/utils/scrollManager'

// Mock DOM 环境
const mockDocument = {
  body: {
    style: {
      overflow: 'unset'
    }
  },
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  createElement: vi.fn(() => ({
    style: {},
    setAttribute: vi.fn(),
    getAttribute: vi.fn(),
    appendChild: vi.fn(),
    removeChild: vi.fn()
  }))
}

const mockWindow = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
}

// 设置全局 mock
Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
})

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
})

describe('ScrollManager', () => {
  beforeEach(() => {
    // 重置 mock
    vi.clearAllMocks()
    mockDocument.body.style.overflow = 'unset'
    
    // 清理所有锁定
    scrollManager.forceUnlockAll()
  })

  afterEach(() => {
    // 确保测试后清理状态
    scrollManager.forceUnlockAll()
  })

  describe('基础锁定功能', () => {
    it('应该能够锁定滚动', () => {
      // 执行锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(true)
      expect(status.locksCount).toBe(1)
      expect(mockDocument.body.style.overflow).toBe('hidden')
    })

    it('应该能够释放锁定', () => {
      // 先锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      // 释放锁定
      scrollManager.unlock('test-1')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(false)
      expect(status.locksCount).toBe(0)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })

    it('应该支持多个锁定', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'Modal1')
      scrollManager.lock('test-2', ScrollLockReason.DIALOG, 'Dialog1')
      scrollManager.lock('test-3', ScrollLockReason.MODAL, 'Modal2')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(true)
      expect(status.locksCount).toBe(3)
      expect(mockDocument.body.style.overflow).toBe('hidden')
    })

    it('应该在所有锁定释放后恢复滚动', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'Modal1')
      scrollManager.lock('test-2', ScrollLockReason.DIALOG, 'Dialog1')
      
      // 部分释放
      scrollManager.unlock('test-1')
      expect(scrollManager.getStatus().isLocked).toBe(true)
      
      // 完全释放
      scrollManager.unlock('test-2')
      expect(scrollManager.getStatus().isLocked).toBe(false)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })
  })

  describe('错误处理', () => {
    it('应该处理空ID的情况', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      scrollManager.lock('', ScrollLockReason.MODAL)
      scrollManager.unlock('')
      
      expect(consoleSpy).toHaveBeenCalledTimes(2)
      expect(scrollManager.getStatus().locksCount).toBe(0)
      
      consoleSpy.mockRestore()
    })

    it('应该处理释放不存在的锁定', () => {
      scrollManager.unlock('non-existent')
      
      // 不应该影响状态
      expect(scrollManager.getStatus().locksCount).toBe(0)
    })

    it('应该能够强制释放所有锁定', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL)
      scrollManager.lock('test-2', ScrollLockReason.DIALOG)
      scrollManager.lock('test-3', ScrollLockReason.MODAL)
      
      // 强制释放
      scrollManager.forceUnlockAll()
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(false)
      expect(status.locksCount).toBe(0)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })
  })

  describe('状态查询', () => {
    it('应该正确返回状态信息', () => {
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      const status = scrollManager.getStatus()
      expect(status).toMatchObject({
        isLocked: true,
        locksCount: 1,
        originalOverflow: 'unset'
      })
      expect(status.locks).toHaveLength(1)
      expect(status.locks[0]).toMatchObject({
        id: 'test-1',
        reason: ScrollLockReason.MODAL,
        component: 'TestComponent'
      })
    })

    it('应该能够按原因查询锁定', () => {
      scrollManager.lock('modal-1', ScrollLockReason.MODAL)
      scrollManager.lock('dialog-1', ScrollLockReason.DIALOG)
      
      expect(scrollManager.hasLockByReason(ScrollLockReason.MODAL)).toBe(true)
      expect(scrollManager.hasLockByReason(ScrollLockReason.DIALOG)).toBe(true)
      expect(scrollManager.hasLockByReason(ScrollLockReason.LOADING)).toBe(false)
    })
  })
})

describe('useModalScrollLock Hook', () => {
  it('应该在组件挂载时锁定滚动', () => {
    const { result } = renderHook(() => useModalScrollLock(true, 'TestModal'))
    
    // 验证锁定已创建
    const status = scrollManager.getStatus()
    expect(status.isLocked).toBe(true)
    expect(status.locksCount).toBe(1)
    
    // 验证返回的函数
    expect(result.current.lock).toBeInstanceOf(Function)
    expect(result.current.unlock).toBeInstanceOf(Function)
    expect(result.current.lockId).toBeDefined()
  })

  it('应该在组件卸载时释放锁定', () => {
    const { unmount } = renderHook(() => useModalScrollLock(true, 'TestModal'))
    
    // 验证锁定已创建
    expect(scrollManager.getStatus().isLocked).toBe(true)
    
    // 卸载组件
    unmount()
    
    // 验证锁定已释放
    expect(scrollManager.getStatus().isLocked).toBe(false)
    expect(scrollManager.getStatus().locksCount).toBe(0)
  })

  it('应该根据enabled参数控制锁定', () => {
    const { result, rerender } = renderHook(
      ({ enabled }) => useModalScrollLock(enabled, 'TestModal'),
      { initialProps: { enabled: false } }
    )
    
    // 初始状态不应该锁定
    expect(scrollManager.getStatus().isLocked).toBe(false)
    
    // 启用锁定
    rerender({ enabled: true })
    expect(scrollManager.getStatus().isLocked).toBe(true)
    
    // 禁用锁定
    rerender({ enabled: false })
    expect(scrollManager.getStatus().isLocked).toBe(false)
  })
})

describe('useDialogScrollLock Hook', () => {
  it('应该使用正确的锁定原因', () => {
    renderHook(() => useDialogScrollLock(true, 'TestDialog'))
    
    const status = scrollManager.getStatus()
    expect(status.locks[0].reason).toBe(ScrollLockReason.DIALOG)
    expect(status.locks[0].component).toBe('TestDialog')
  })
})

describe('并发场景测试', () => {
  it('应该正确处理多个组件同时锁定和释放', () => {
    // 模拟多个组件同时使用
    const { unmount: unmount1 } = renderHook(() => useModalScrollLock(true, 'Modal1'))
    const { unmount: unmount2 } = renderHook(() => useModalScrollLock(true, 'Modal2'))
    const { unmount: unmount3 } = renderHook(() => useDialogScrollLock(true, 'Dialog1'))
    
    // 验证所有锁定都已创建
    expect(scrollManager.getStatus().locksCount).toBe(3)
    expect(scrollManager.getStatus().isLocked).toBe(true)
    
    // 部分卸载
    unmount1()
    expect(scrollManager.getStatus().locksCount).toBe(2)
    expect(scrollManager.getStatus().isLocked).toBe(true)
    
    unmount2()
    expect(scrollManager.getStatus().locksCount).toBe(1)
    expect(scrollManager.getStatus().isLocked).toBe(true)
    
    // 最后一个卸载
    unmount3()
    expect(scrollManager.getStatus().locksCount).toBe(0)
    expect(scrollManager.getStatus().isLocked).toBe(false)
  })

  it('应该处理快速切换场景', () => {
    // 快速创建和销毁多个锁定
    for (let i = 0; i < 10; i++) {
      const lockId = `rapid-test-${i}`
      scrollManager.lock(lockId, ScrollLockReason.MODAL)
      scrollManager.unlock(lockId)
    }
    
    // 最终状态应该是清洁的
    const status = scrollManager.getStatus()
    expect(status.isLocked).toBe(false)
    expect(status.locksCount).toBe(0)
  })
})

describe('调试功能', () => {
  it('应该能够启用和禁用调试模式', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    // 启用调试模式
    scrollManager.enableDebug()
    scrollManager.lock('debug-test', ScrollLockReason.MODAL)
    
    expect(consoleSpy).toHaveBeenCalled()
    
    // 禁用调试模式
    scrollManager.disableDebug()
    scrollManager.unlock('debug-test')
    
    consoleSpy.mockRestore()
  })
})
