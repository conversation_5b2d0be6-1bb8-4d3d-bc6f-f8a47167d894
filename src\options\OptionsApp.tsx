import React, { useCallback, useEffect } from 'react'
import { Card } from '../components/ui/card'
// 自定义组件
import AppHeader from './components/AppHeader'
import NavigationSidebar from './components/NavigationSidebar'
import LoadingState from './components/LoadingState'
import ErrorState from './components/ErrorState'
import TabContentRenderer from './components/TabContentRenderer'
// Hooks
import { useResponsive } from './hooks/useResponsive'
import { useTabNavigation } from './hooks/useTabNavigation'
import { useAppInitialization } from './hooks/useAppInitialization'
// 配置
import { getFilteredTabs } from './constants/tabsConfig'
// 滚动监控
import { startDefaultMonitoring, stopScrollMonitoring } from '../utils/scrollMonitor'

interface OptionsAppProps {}

const OptionsApp: React.FC<OptionsAppProps> = () => {
  // 自定义Hooks
  const { isMobile, getResponsiveValue } = useResponsive()
  const { loading, initError, retryCount, maxRetries, handleRetry } = useAppInitialization()
  
  // 获取过滤后的标签页列表
  const tabs = getFilteredTabs()
  const { activeTab, handleTabChange } = useTabNavigation({ tabs })

  // 错误处理函数
  const handleTabError = useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    console.error(`标签页 ${activeTab} 发生错误:`, error, errorInfo)

    // 可以在这里添加错误报告逻辑
    // reportError({ tab: activeTab, error, errorInfo })
  }, [activeTab])

  // 初始化滚动监控
  useEffect(() => {
    // 启动滚动监控
    startDefaultMonitoring()

    // 组件卸载时停止监控
    return () => {
      stopScrollMonitoring()
    }
  }, [])



  // 渲染加载状态
  if (loading) {
    return <LoadingState retryCount={retryCount} maxRetries={maxRetries} />
  }

  // 渲染初始化错误状态
  if (initError) {
    return (
      <ErrorState 
        initError={initError}
        retryCount={retryCount}
        maxRetries={maxRetries}
        onRetry={handleRetry}
      />
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 头部 */}
      <AppHeader isMobile={isMobile} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'space-x-8'}`}>
          {/* 侧边栏导航 */}
          <NavigationSidebar
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            isMobile={isMobile}
            getResponsiveValue={getResponsiveValue}
          />

          {/* 主内容区域 */}
          <main 
            className="flex-1 main-content-stable" 
            role="tabpanel"
            aria-labelledby={`tab-${activeTab}`}
            tabIndex={-1}
          >
            <Card>
              <TabContentRenderer activeTab={activeTab} onError={handleTabError} />
            </Card>
          </main>
        </div>
      </div>
    </div>
  )
}

export default OptionsApp