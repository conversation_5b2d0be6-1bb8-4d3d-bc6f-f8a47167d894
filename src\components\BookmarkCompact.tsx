// 收藏紧凑视图组件 - 实现紧凑的多行布局

import React from 'react'
import { ExternalLink, Edit, Trash2, Star, Clock, Tag } from 'lucide-react'
import TruncatedTitle from './TruncatedTitle'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from './ui/tooltip'
import { useTagColors } from '../hooks/useTagColors'
import type { Bookmark } from '../types'

interface BookmarkCompactProps {
  /** 收藏数据 */
  bookmark: Bookmark
  /** 是否高亮显示 */
  isHighlighted?: boolean
  /** 编辑回调 */
  onEdit?: (bookmark: Bookmark) => void
  /** 删除回调 */
  onDelete?: (bookmark: Bookmark) => void
  /** 点击回调 */
  onClick?: (bookmark: Bookmark) => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 收藏紧凑视图组件
 * 实现紧凑的多行布局，信息密集但清晰的显示
 * 适合在有限空间内展示更多收藏项目
 */
const BookmarkCompact: React.FC<BookmarkCompactProps> = React.memo(({
  bookmark,
  isHighlighted = false,
  onEdit,
  onDelete,
  onClick,
  className = ''
}) => {
  // 获取标签颜色
  const { getTagColor } = useTagColors()
  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    // 如果点击的是操作按钮，不触发行点击
    if ((e.target as HTMLElement).closest('.bookmark-compact-actions')) {
      return
    }
    onClick?.(bookmark)
  }

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit?.(bookmark)
  }

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete?.(bookmark)
  }

  // 处理外部链接点击
  const handleLinkClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (bookmark.url) {
      window.open(bookmark.url, '_blank', 'noopener,noreferrer')
    }
  }

  // 格式化时间显示
  const formatTime = (date: Date | string) => {
    // 确保日期是Date对象
    const dateObj = typeof date === 'string' ? new Date(date) : date
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '未知时间'
    }
    
    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return dateObj.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  return (
    <TooltipProvider>
      <Card
        className={`
          group relative cursor-pointer hover:shadow-md
          ${isHighlighted ? 'ring-2 ring-primary border-primary bg-accent' : ''}
          ${className}
        `}
        onClick={handleClick}
      >
        <CardContent className="p-3">
      {/* 头部：网站图标 + 标题 + 操作按钮 */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-start space-x-2 flex-1 min-w-0">
          {/* 网站图标 */}
          <div className="flex-shrink-0 w-4 h-4 mt-0.5">
            {bookmark.favicon ? (
              <img 
                src={bookmark.favicon} 
                alt="" 
                className="w-4 h-4 rounded-sm"
                onError={(e) => {
                  // 图标加载失败时显示默认图标
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  target.nextElementSibling?.classList.remove('hidden')
                }}
              />
            ) : null}
            <Star className={`w-4 h-4 text-muted-foreground ${bookmark.favicon ? 'hidden' : ''}`} />
          </div>

          {/* 标题 */}
          <div className="flex-1 min-w-0">
            <TruncatedTitle
              title={bookmark.title || '无标题'}
              maxLength={50}
              useContainerWidth={true}
              className="text-sm font-medium text-foreground group-hover:text-primary transition-colors leading-tight"
            />
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="bookmark-compact-actions flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
          {/* 外部链接按钮 */}
          {bookmark.url && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleLinkClick}
                  className="h-6 w-6 text-muted-foreground hover:text-primary"
                  aria-label="在新标签页中打开"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>在新标签页中打开</p>
              </TooltipContent>
            </Tooltip>
          )}

          {/* 编辑按钮 */}
          {onEdit && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleEditClick}
                  className="h-6 w-6 text-muted-foreground hover:text-blue-600"
                  aria-label="编辑收藏"
                >
                  <Edit className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑收藏</p>
              </TooltipContent>
            </Tooltip>
          )}

          {/* 删除按钮 */}
          {onDelete && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDeleteClick}
                  className="h-6 w-6 text-muted-foreground hover:text-destructive"
                  aria-label="删除收藏"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>删除收藏</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {/* URL行 */}
      {bookmark.url && (
        <div className="mb-2">
          <TruncatedTitle
            title={bookmark.url}
            maxLength={60}
            useContainerWidth={true}
            className="text-xs text-muted-foreground hover:text-primary transition-colors"
          />
        </div>
      )}

      {/* 描述行 */}
      {bookmark.description && (
        <div className="mb-2">
          <TruncatedTitle
            title={bookmark.description}
            maxLength={80}
            useContainerWidth={true}
            maxLines={2}
            className="text-xs text-muted-foreground leading-relaxed"
          />
        </div>
      )}

      {/* 内容预览（如果是文本类型） */}
      {bookmark.content && bookmark.type === 'text' && (
        <div className="mb-2 bg-muted rounded p-2">
          <TruncatedTitle
            title={bookmark.content}
            maxLength={100}
            useContainerWidth={true}
            maxLines={3}
            className="text-xs text-muted-foreground leading-relaxed"
          />
        </div>
      )}

      {/* 底部信息栏：分类 + 标签 + 时间 */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          {/* 分类 */}
          {bookmark.category && (
            <Badge variant="secondary" className="text-xs flex-shrink-0">
              {bookmark.category}
            </Badge>
          )}

          {/* 标签 */}
          {bookmark.tags && bookmark.tags.length > 0 && (
            <div className="flex items-center space-x-1 min-w-0">
              <Tag className="w-3 h-3 text-muted-foreground flex-shrink-0" />
              <div className="flex items-center space-x-1 min-w-0 overflow-hidden">
                {bookmark.tags.slice(0, 2).map((tagName) => (
                  <Badge 
                    key={tagName} 
                    variant="outline"
                    className="text-xs flex-shrink-0 border"
                    style={{ 
                      borderColor: getTagColor(tagName),
                      backgroundColor: `${getTagColor(tagName)}15`,
                      color: getTagColor(tagName)
                    }}
                  >
                    {tagName}
                  </Badge>
                ))}
                {bookmark.tags.length > 2 && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-xs text-muted-foreground flex-shrink-0 cursor-help">
                        +{bookmark.tags.length - 2}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        {bookmark.tags.slice(2).map((tagName) => (
                          <div key={tagName} className="flex items-center space-x-2">
                            <div 
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: getTagColor(tagName) }}
                            />
                            <span>{tagName}</span>
                          </div>
                        ))}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 时间信息 */}
        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
          <Clock className="w-3 h-3" />
          <span>{formatTime(bookmark.createdAt)}</span>
        </div>
      </div>

        {/* 类型指示器（右上角小标记） */}
        {bookmark.type !== 'url' && (
          <div className="absolute top-2 right-2">
            <div className={`
              w-2 h-2 rounded-full
              ${bookmark.type === 'text' ? 'bg-blue-400' : bookmark.type === 'image' ? 'bg-green-400' : 'bg-muted-foreground'}
            `} title={`类型: ${bookmark.type}`} />
          </div>
        )}
        </CardContent>
      </Card>
    </TooltipProvider>
  )
})

// 设置显示名称便于调试
BookmarkCompact.displayName = 'BookmarkCompact'

export default BookmarkCompact

// 导出类型定义
export type { BookmarkCompactProps }