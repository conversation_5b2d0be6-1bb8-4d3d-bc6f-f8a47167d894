/**
 * 滚动管理器基础功能测试
 * 专注于测试核心的滚动锁定逻辑，不依赖React环境
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ScrollLockReason } from '../src/utils/scrollManager'

// 创建一个简化的滚动管理器用于测试
class TestScrollManager {
  private locks: Map<string, any> = new Map()
  private isLocked: boolean = false
  private originalOverflow: string = 'unset'
  private debugMode: boolean = false

  constructor() {
    // 模拟DOM环境
    if (typeof document !== 'undefined') {
      this.originalOverflow = document.body?.style?.overflow || 'unset'
    }
  }

  enableDebug() {
    this.debugMode = true
  }

  disableDebug() {
    this.debugMode = false
  }

  private log(message: string, data?: any) {
    if (this.debugMode) {
      console.log(`[TestScrollManager] ${message}`, data || '')
    }
  }

  lock(id: string, reason: ScrollLockReason = ScrollLockReason.CUSTOM, component?: string): void {
    if (!id) {
      console.warn('[TestScrollManager] 锁定ID不能为空')
      return
    }

    const record = {
      id,
      reason,
      timestamp: Date.now(),
      component
    }

    this.locks.set(id, record)
    this.log(`锁定滚动: ${id}`, record)

    if (!this.isLocked) {
      if (typeof document !== 'undefined' && document.body) {
        this.originalOverflow = document.body.style.overflow || 'unset'
        document.body.style.overflow = 'hidden'
      }
      this.isLocked = true
      this.log('应用滚动锁定', { originalOverflow: this.originalOverflow })
    }
  }

  unlock(id: string): void {
    if (!id) {
      console.warn('[TestScrollManager] 解锁ID不能为空')
      return
    }

    const record = this.locks.get(id)
    if (record) {
      this.locks.delete(id)
      this.log(`释放滚动锁定: ${id}`, record)
    } else {
      this.log(`尝试释放不存在的锁定: ${id}`)
    }

    if (this.locks.size === 0 && this.isLocked) {
      this.restoreScroll()
    }
  }

  forceUnlockAll(): void {
    this.log('强制释放所有滚动锁定', { locksCount: this.locks.size })
    this.locks.clear()
    this.restoreScroll()
  }

  private restoreScroll(): void {
    if (this.isLocked) {
      if (typeof document !== 'undefined' && document.body) {
        document.body.style.overflow = this.originalOverflow
      }
      this.isLocked = false
      this.log('恢复滚动状态', { overflow: this.originalOverflow })
    }
  }

  getStatus() {
    return {
      isLocked: this.isLocked,
      locksCount: this.locks.size,
      locks: Array.from(this.locks.values()),
      originalOverflow: this.originalOverflow
    }
  }

  hasLockByReason(reason: ScrollLockReason): boolean {
    return Array.from(this.locks.values()).some(lock => lock.reason === reason)
  }
}

// Mock DOM 环境
const mockDocument = {
  body: {
    style: {
      overflow: 'unset'
    }
  }
}

// 设置全局 mock
Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
})

describe('ScrollManager 基础功能测试', () => {
  let scrollManager: TestScrollManager

  beforeEach(() => {
    // 重置 mock
    mockDocument.body.style.overflow = 'unset'
    
    // 创建新的滚动管理器实例
    scrollManager = new TestScrollManager()
  })

  afterEach(() => {
    // 确保测试后清理状态
    scrollManager.forceUnlockAll()
  })

  describe('基础锁定功能', () => {
    it('应该能够锁定滚动', () => {
      // 执行锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(true)
      expect(status.locksCount).toBe(1)
      expect(mockDocument.body.style.overflow).toBe('hidden')
    })

    it('应该能够释放锁定', () => {
      // 先锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      // 释放锁定
      scrollManager.unlock('test-1')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(false)
      expect(status.locksCount).toBe(0)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })

    it('应该支持多个锁定', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'Modal1')
      scrollManager.lock('test-2', ScrollLockReason.DIALOG, 'Dialog1')
      scrollManager.lock('test-3', ScrollLockReason.MODAL, 'Modal2')
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(true)
      expect(status.locksCount).toBe(3)
      expect(mockDocument.body.style.overflow).toBe('hidden')
    })

    it('应该在所有锁定释放后恢复滚动', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'Modal1')
      scrollManager.lock('test-2', ScrollLockReason.DIALOG, 'Dialog1')
      
      // 部分释放
      scrollManager.unlock('test-1')
      expect(scrollManager.getStatus().isLocked).toBe(true)
      expect(mockDocument.body.style.overflow).toBe('hidden')
      
      // 完全释放
      scrollManager.unlock('test-2')
      expect(scrollManager.getStatus().isLocked).toBe(false)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })
  })

  describe('错误处理', () => {
    it('应该处理空ID的情况', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      scrollManager.lock('', ScrollLockReason.MODAL)
      scrollManager.unlock('')
      
      expect(consoleSpy).toHaveBeenCalledTimes(2)
      expect(scrollManager.getStatus().locksCount).toBe(0)
      
      consoleSpy.mockRestore()
    })

    it('应该处理释放不存在的锁定', () => {
      scrollManager.unlock('non-existent')
      
      // 不应该影响状态
      expect(scrollManager.getStatus().locksCount).toBe(0)
    })

    it('应该能够强制释放所有锁定', () => {
      // 创建多个锁定
      scrollManager.lock('test-1', ScrollLockReason.MODAL)
      scrollManager.lock('test-2', ScrollLockReason.DIALOG)
      scrollManager.lock('test-3', ScrollLockReason.MODAL)
      
      // 强制释放
      scrollManager.forceUnlockAll()
      
      // 验证状态
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(false)
      expect(status.locksCount).toBe(0)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })
  })

  describe('状态查询', () => {
    it('应该正确返回状态信息', () => {
      scrollManager.lock('test-1', ScrollLockReason.MODAL, 'TestComponent')
      
      const status = scrollManager.getStatus()
      expect(status).toMatchObject({
        isLocked: true,
        locksCount: 1,
        originalOverflow: 'unset'
      })
      expect(status.locks).toHaveLength(1)
      expect(status.locks[0]).toMatchObject({
        id: 'test-1',
        reason: ScrollLockReason.MODAL,
        component: 'TestComponent'
      })
    })

    it('应该能够按原因查询锁定', () => {
      scrollManager.lock('modal-1', ScrollLockReason.MODAL)
      scrollManager.lock('dialog-1', ScrollLockReason.DIALOG)
      
      expect(scrollManager.hasLockByReason(ScrollLockReason.MODAL)).toBe(true)
      expect(scrollManager.hasLockByReason(ScrollLockReason.DIALOG)).toBe(true)
      expect(scrollManager.hasLockByReason(ScrollLockReason.LOADING)).toBe(false)
    })
  })

  describe('并发场景测试', () => {
    it('应该处理快速切换场景', () => {
      // 快速创建和销毁多个锁定
      for (let i = 0; i < 10; i++) {
        const lockId = `rapid-test-${i}`
        scrollManager.lock(lockId, ScrollLockReason.MODAL)
        scrollManager.unlock(lockId)
      }
      
      // 最终状态应该是清洁的
      const status = scrollManager.getStatus()
      expect(status.isLocked).toBe(false)
      expect(status.locksCount).toBe(0)
      expect(mockDocument.body.style.overflow).toBe('unset')
    })

    it('应该正确处理交错的锁定和释放', () => {
      // 创建多个锁定
      scrollManager.lock('lock-1', ScrollLockReason.MODAL)
      scrollManager.lock('lock-2', ScrollLockReason.DIALOG)
      scrollManager.lock('lock-3', ScrollLockReason.MODAL)
      
      // 交错释放
      scrollManager.unlock('lock-2') // 中间的
      expect(scrollManager.getStatus().locksCount).toBe(2)
      expect(scrollManager.getStatus().isLocked).toBe(true)
      
      scrollManager.unlock('lock-1') // 第一个
      expect(scrollManager.getStatus().locksCount).toBe(1)
      expect(scrollManager.getStatus().isLocked).toBe(true)
      
      scrollManager.unlock('lock-3') // 最后一个
      expect(scrollManager.getStatus().locksCount).toBe(0)
      expect(scrollManager.getStatus().isLocked).toBe(false)
    })
  })

  describe('调试功能', () => {
    it('应该能够启用和禁用调试模式', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      // 启用调试模式
      scrollManager.enableDebug()
      scrollManager.lock('debug-test', ScrollLockReason.MODAL)
      
      expect(consoleSpy).toHaveBeenCalled()
      
      // 禁用调试模式
      scrollManager.disableDebug()
      scrollManager.unlock('debug-test')
      
      consoleSpy.mockRestore()
    })
  })
})
