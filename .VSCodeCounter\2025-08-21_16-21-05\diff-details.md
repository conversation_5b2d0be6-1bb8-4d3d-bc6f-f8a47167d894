# Diff Details

Date : 2025-08-21 16:21:05

Directory d:\\mydev\\Qiankun-Pouch\\src

Total : 38 files,  5452 codes, 912 comments, 689 blanks, all 7053 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/background/messageHandler.ts](/src/background/messageHandler.ts) | TypeScript | 61 | 12 | 8 | 81 |
| [src/components/AIIntegrationTab.tsx](/src/components/AIIntegrationTab.tsx) | TypeScript JSX | 129 | 8 | 13 | 150 |
| [src/components/AIRecommendations.tsx](/src/components/AIRecommendations.tsx) | TypeScript JSX | 553 | 131 | 92 | 776 |
| [src/components/BookmarkEditModal.tsx](/src/components/BookmarkEditModal.tsx) | TypeScript JSX | 252 | 25 | 31 | 308 |
| [src/components/CategoryCard.tsx](/src/components/CategoryCard.tsx) | TypeScript JSX | 48 | 2 | 2 | 52 |
| [src/components/CategoryManagementTab.tsx](/src/components/CategoryManagementTab.tsx) | TypeScript JSX | -23 | -4 | -3 | -30 |
| [src/components/DefaultAIModelsTab.tsx](/src/components/DefaultAIModelsTab.tsx) | TypeScript JSX | 111 | 11 | 12 | 134 |
| [src/components/MCPSettingsTab.tsx](/src/components/MCPSettingsTab.tsx) | TypeScript JSX | 203 | 13 | 21 | 237 |
| [src/components/ManagementPageLayout.tsx](/src/components/ManagementPageLayout.tsx) | TypeScript JSX | 81 | 25 | 11 | 117 |
| [src/components/ModelSelector.tsx](/src/components/ModelSelector.tsx) | TypeScript JSX | 3 | 0 | 0 | 3 |
| [src/components/SmartFolderSelector.tsx](/src/components/SmartFolderSelector.tsx) | TypeScript JSX | 274 | 54 | 34 | 362 |
| [src/components/SmartTagInput.tsx](/src/components/SmartTagInput.tsx) | TypeScript JSX | 296 | 56 | 31 | 383 |
| [src/components/TagCard.tsx](/src/components/TagCard.tsx) | TypeScript JSX | 17 | 0 | 0 | 17 |
| [src/components/TagManagementTab.tsx](/src/components/TagManagementTab.tsx) | TypeScript JSX | -21 | -4 | -3 | -28 |
| [src/demo/management-layout-demo.html](/src/demo/management-layout-demo.html) | HTML | 13 | 0 | 1 | 14 |
| [src/demo/management-layout-demo.tsx](/src/demo/management-layout-demo.tsx) | TypeScript JSX | 10 | 1 | 3 | 14 |
| [src/examples/ManagementLayoutDemo.tsx](/src/examples/ManagementLayoutDemo.tsx) | TypeScript JSX | 168 | 4 | 15 | 187 |
| [src/options/data/helpContent.ts](/src/options/data/helpContent.ts) | TypeScript | 47 | 0 | -47 | 0 |
| [src/popup/PopupApp.tsx](/src/popup/PopupApp.tsx) | TypeScript JSX | 60 | 4 | 3 | 67 |
| [src/popup/components/DetailedBookmarkForm.tsx](/src/popup/components/DetailedBookmarkForm.tsx) | TypeScript JSX | 252 | 26 | 29 | 307 |
| [src/popup/components/QuickBookmarkForm.tsx](/src/popup/components/QuickBookmarkForm.tsx) | TypeScript JSX | 209 | 47 | 22 | 278 |
| [src/popup/components/SmartRecognition.tsx](/src/popup/components/SmartRecognition.tsx) | TypeScript JSX | 197 | 41 | 24 | 262 |
| [src/services/BookmarkImportExportService.ts](/src/services/BookmarkImportExportService.ts) | TypeScript | 20 | -8 | -12 | 0 |
| [src/services/aiChatService.ts](/src/services/aiChatService.ts) | TypeScript | 53 | 5 | 9 | 67 |
| [src/services/aiIntegrationService.ts](/src/services/aiIntegrationService.ts) | TypeScript | 22 | 13 | 8 | 43 |
| [src/services/aiProviderService.ts](/src/services/aiProviderService.ts) | TypeScript | 441 | 31 | 25 | 497 |
| [src/services/aiRecommendationService.ts](/src/services/aiRecommendationService.ts) | TypeScript | 470 | 122 | 99 | 691 |
| [src/services/aiService.ts](/src/services/aiService.ts) | TypeScript | 651 | 151 | 108 | 910 |
| [src/services/categoryService.ts](/src/services/categoryService.ts) | TypeScript | 8 | 4 | 1 | 13 |
| [src/services/defaultAIModelAPI.ts](/src/services/defaultAIModelAPI.ts) | TypeScript | 9 | 0 | 3 | 12 |
| [src/services/defaultAIModelService.ts](/src/services/defaultAIModelService.ts) | TypeScript | 94 | 31 | 20 | 145 |
| [src/services/mcpTestService.ts](/src/services/mcpTestService.ts) | TypeScript | 208 | 59 | 41 | 308 |
| [src/styles/globals.css](/src/styles/globals.css) | PostCSS | 92 | 3 | 21 | 116 |
| [src/styles/responsive.css](/src/styles/responsive.css) | PostCSS | 41 | 3 | 8 | 52 |
| [src/test-pages/management-ui-test.html](/src/test-pages/management-ui-test.html) | HTML | 20 | 0 | 1 | 21 |
| [src/test-pages/management-ui-test.tsx](/src/test-pages/management-ui-test.tsx) | TypeScript JSX | 196 | 4 | 15 | 215 |
| [src/types/messages.ts](/src/types/messages.ts) | TypeScript | 68 | 3 | 6 | 77 |
| [src/utils/indexedDB.ts](/src/utils/indexedDB.ts) | TypeScript | 119 | 39 | 37 | 195 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details