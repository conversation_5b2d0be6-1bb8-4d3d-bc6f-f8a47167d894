Date : 2025-08-21 16:21:05
Directory : d:\mydev\Qiankun-Pouch\src
Total : 38 files,  5452 codes, 912 comments, 689 blanks, all 7053 lines

Languages
+----------------+------------+------------+------------+------------+------------+
| language       | files      | code       | comment    | blank      | total      |
+----------------+------------+------------+------------+------------+------------+
| TypeScript JSX |         20 |      3,015 |        444 |        352 |      3,811 |
| TypeScript     |         14 |      2,271 |        462 |        306 |      3,039 |
| PostCSS        |          2 |        133 |          6 |         29 |        168 |
| HTML           |          2 |         33 |          0 |          2 |         35 |
+----------------+------------+------------+------------+------------+------------+

Directories
+----------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                 | files      | code       | comment    | blank      | total      |
+----------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                    |         38 |      5,452 |        912 |        689 |      7,053 |
| background                                                           |          1 |         61 |         12 |          8 |         81 |
| components                                                           |         13 |      1,923 |        317 |        241 |      2,481 |
| demo                                                                 |          2 |         23 |          1 |          4 |         28 |
| examples                                                             |          1 |        168 |          4 |         15 |        187 |
| options                                                              |          1 |         47 |          0 |        -47 |          0 |
| options\data                                                         |          1 |         47 |          0 |        -47 |          0 |
| popup                                                                |          4 |        718 |        118 |         78 |        914 |
| popup (Files)                                                        |          1 |         60 |          4 |          3 |         67 |
| popup\components                                                     |          3 |        658 |        114 |         75 |        847 |
| services                                                             |         10 |      1,976 |        408 |        302 |      2,686 |
| styles                                                               |          2 |        133 |          6 |         29 |        168 |
| test-pages                                                           |          2 |        216 |          4 |         16 |        236 |
| types                                                                |          1 |         68 |          3 |          6 |         77 |
| utils                                                                |          1 |        119 |         39 |         37 |        195 |
+----------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+----------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| filename                                                             | language       | code       | comment    | blank      | total      |
+----------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| d:\mydev\Qiankun-Pouch\src\background\messageHandler.ts              | TypeScript     |         61 |         12 |          8 |         81 |
| d:\mydev\Qiankun-Pouch\src\components\AIIntegrationTab.tsx           | TypeScript JSX |        129 |          8 |         13 |        150 |
| d:\mydev\Qiankun-Pouch\src\components\AIRecommendations.tsx          | TypeScript JSX |        553 |        131 |         92 |        776 |
| d:\mydev\Qiankun-Pouch\src\components\BookmarkEditModal.tsx          | TypeScript JSX |        252 |         25 |         31 |        308 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryCard.tsx               | TypeScript JSX |         48 |          2 |          2 |         52 |
| d:\mydev\Qiankun-Pouch\src\components\CategoryManagementTab.tsx      | TypeScript JSX |        -23 |         -4 |         -3 |        -30 |
| d:\mydev\Qiankun-Pouch\src\components\DefaultAIModelsTab.tsx         | TypeScript JSX |        111 |         11 |         12 |        134 |
| d:\mydev\Qiankun-Pouch\src\components\MCPSettingsTab.tsx             | TypeScript JSX |        203 |         13 |         21 |        237 |
| d:\mydev\Qiankun-Pouch\src\components\ManagementPageLayout.tsx       | TypeScript JSX |         81 |         25 |         11 |        117 |
| d:\mydev\Qiankun-Pouch\src\components\ModelSelector.tsx              | TypeScript JSX |          3 |          0 |          0 |          3 |
| d:\mydev\Qiankun-Pouch\src\components\SmartFolderSelector.tsx        | TypeScript JSX |        274 |         54 |         34 |        362 |
| d:\mydev\Qiankun-Pouch\src\components\SmartTagInput.tsx              | TypeScript JSX |        296 |         56 |         31 |        383 |
| d:\mydev\Qiankun-Pouch\src\components\TagCard.tsx                    | TypeScript JSX |         17 |          0 |          0 |         17 |
| d:\mydev\Qiankun-Pouch\src\components\TagManagementTab.tsx           | TypeScript JSX |        -21 |         -4 |         -3 |        -28 |
| d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.html          | HTML           |         13 |          0 |          1 |         14 |
| d:\mydev\Qiankun-Pouch\src\demo\management-layout-demo.tsx           | TypeScript JSX |         10 |          1 |          3 |         14 |
| d:\mydev\Qiankun-Pouch\src\examples\ManagementLayoutDemo.tsx         | TypeScript JSX |        168 |          4 |         15 |        187 |
| d:\mydev\Qiankun-Pouch\src\options\data\helpContent.ts               | TypeScript     |         47 |          0 |        -47 |          0 |
| d:\mydev\Qiankun-Pouch\src\popup\PopupApp.tsx                        | TypeScript JSX |         60 |          4 |          3 |         67 |
| d:\mydev\Qiankun-Pouch\src\popup\components\DetailedBookmarkForm.tsx | TypeScript JSX |        252 |         26 |         29 |        307 |
| d:\mydev\Qiankun-Pouch\src\popup\components\QuickBookmarkForm.tsx    | TypeScript JSX |        209 |         47 |         22 |        278 |
| d:\mydev\Qiankun-Pouch\src\popup\components\SmartRecognition.tsx     | TypeScript JSX |        197 |         41 |         24 |        262 |
| d:\mydev\Qiankun-Pouch\src\services\BookmarkImportExportService.ts   | TypeScript     |         20 |         -8 |        -12 |          0 |
| d:\mydev\Qiankun-Pouch\src\services\aiChatService.ts                 | TypeScript     |         53 |          5 |          9 |         67 |
| d:\mydev\Qiankun-Pouch\src\services\aiIntegrationService.ts          | TypeScript     |         22 |         13 |          8 |         43 |
| d:\mydev\Qiankun-Pouch\src\services\aiProviderService.ts             | TypeScript     |        441 |         31 |         25 |        497 |
| d:\mydev\Qiankun-Pouch\src\services\aiRecommendationService.ts       | TypeScript     |        470 |        122 |         99 |        691 |
| d:\mydev\Qiankun-Pouch\src\services\aiService.ts                     | TypeScript     |        651 |        151 |        108 |        910 |
| d:\mydev\Qiankun-Pouch\src\services\categoryService.ts               | TypeScript     |          8 |          4 |          1 |         13 |
| d:\mydev\Qiankun-Pouch\src\services\defaultAIModelAPI.ts             | TypeScript     |          9 |          0 |          3 |         12 |
| d:\mydev\Qiankun-Pouch\src\services\defaultAIModelService.ts         | TypeScript     |         94 |         31 |         20 |        145 |
| d:\mydev\Qiankun-Pouch\src\services\mcpTestService.ts                | TypeScript     |        208 |         59 |         41 |        308 |
| d:\mydev\Qiankun-Pouch\src\styles\globals.css                        | PostCSS        |         92 |          3 |         21 |        116 |
| d:\mydev\Qiankun-Pouch\src\styles\responsive.css                     | PostCSS        |         41 |          3 |          8 |         52 |
| d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.html        | HTML           |         20 |          0 |          1 |         21 |
| d:\mydev\Qiankun-Pouch\src\test-pages\management-ui-test.tsx         | TypeScript JSX |        196 |          4 |         15 |        215 |
| d:\mydev\Qiankun-Pouch\src\types\messages.ts                         | TypeScript     |         68 |          3 |          6 |         77 |
| d:\mydev\Qiankun-Pouch\src\utils\indexedDB.ts                        | TypeScript     |        119 |         39 |         37 |        195 |
| Total                                                                |                |      5,452 |        912 |        689 |      7,053 |
+----------------------------------------------------------------------+----------------+------------+------------+------------+------------+